import { Box, Button as MUIButton } from '@mui/material';
import { useContext } from 'react';
import AddIcon from '@mui/icons-material/Add';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';
import { NEW_CHAT_BUTTON_LABEL } from '../utils/labels';
import { LayoutContext } from '../components/Layout/LayoutContext';

interface NewChatButtonControllerProps {
  onClick: () => void;
  isCollapsed: boolean;
}

const NewChatButtonController: React.FC<NewChatButtonControllerProps> = ({ onClick, isCollapsed }) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const { isSidebarCollapsed, toggleSidebarCollapsed, userExpandedSidebar } = useContext(LayoutContext);


  const handleClick = () => {
    onClick();
    if (isSidebarCollapsed) {
      toggleSidebarCollapsed(false);
    } else {
      if (!userExpandedSidebar) {
        toggleSidebarCollapsed(false);
      }
    }

    navigate(`/`);
  };

  return (
    <Box sx={{ marginBottom: '1rem', px: isCollapsed ? 0.5 : 2 }}>
      <MUIButton
        variant="outlined"
        color="secondary"
        size="medium"
        onClick={handleClick}
        startIcon={<AddIcon />}
        sx={{
          backgroundColor: `${theme.palette.background.default}`,
          borderColor: `${theme.palette.divider}`,
          color: `${theme.palette.text.secondary}`,
          fontSize: '15px',
          fontFamily: 'Roboto',
          fontWeight: 600,
          width: isCollapsed ? '42px' : '100%',
          height: '42px',
          padding: isCollapsed ? '0px' : '8px 22px',
          borderRadius: isCollapsed ? '50%' : '40px',
          minWidth: 'auto',
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          flex: '1 0 0',
          margin: 0,
          '& .MuiButton-startIcon': {
            margin: isCollapsed ? '0px' : '0px 8px 0px -2px',
          },
          '&:hover': {
            backgroundColor: `${theme.palette.background.default}`,
            borderColor: `${theme.palette.secondary.main}`,
            borderWidth: '1px',
            color: `${theme.palette.text.secondary}`,
          },
        }}
      >
        {!isCollapsed && NEW_CHAT_BUTTON_LABEL}
      </MUIButton>
    </Box>
  );
};

export default NewChatButtonController;