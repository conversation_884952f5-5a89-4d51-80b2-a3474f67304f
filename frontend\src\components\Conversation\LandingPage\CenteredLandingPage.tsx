import React, { useState, useEffect } from "react";
import { Box, Chip } from "@mui/material";
import ConversationHeader from "../ConversationHeader";
import { TagEntry } from "../../../types/ConversationTypes";
import { useIsMobile, useIsTablet } from "../../Layout/MobileUtils";
import PromptSection from "../../PromptControl/PromptSection";
import { PROMPT_LABEL } from "../../../utils/labels";
import { useTheme } from "@mui/material/styles";

interface CenteredLandingPageProps {
  handleUserPrompt: (message: string) => void;
}

enum tagTypes {
  Interventions = "interventions",
  Outcomes = "outcomes",
}

const tagList = [
  {
    key: 1,
    type: tagTypes.Interventions,
    label: "Evidence on development program effectiveness",
  },
  {
    key: 2,
    type: tagTypes.Outcomes,
    label: "Evidence on improving development outcomes",
  },
];

const tagSearchResponse = {
  success: true,
  data: {
    entries: [
      {
        type: "FocusIntervention",
        value: [
          "Do unconditional cash transfers reduce malnutrition?",
          "What’s the evidence on computer-aided instruction?",
          "What are the failures of food subsidy programs?",
          "Effectiveness of food transfer programs in Latin America.",
        ],
        label: [
          "Do unconditional cash transfers reduce malnutrition?",
          "What’s the evidence on computer-aided instruction?",
          "What are the failures of food subsidy programs?",
          "Effectiveness of food transfer programs in Latin America.",
        ],
      },
      {
        type: "FocusOutcome",
        value: [
          "Recent programs successful at reducing school dropout.",
          "Summarize the evidence on reducing unintended pregnancy.",
          "Evidence on drought-resistant crop varieties?",
          "Evidence on improving educational enrollment persistence.",
        ],
        label: [
          "Recent programs successful at reducing school dropout.",
          "Summarize the evidence on reducing unintended pregnancy.",
          "Evidence on drought-resistant crop varieties?",
          "Evidence on improving educational enrollment persistence.",
        ],
      },
    ],
  },
};

const CenteredLandingPage: React.FC<CenteredLandingPageProps> = ({
  handleUserPrompt,
}) => {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const isMobileOrTablet = isMobile || isTablet;
  const theme = useTheme();

  type TagTypesKeys = keyof typeof tagTypes;
  type TagTypesValues = (typeof tagTypes)[TagTypesKeys];

  const [dataTags, setDataTags] = useState<any>({
    [tagTypes.Interventions]: [],
    [tagTypes.Outcomes]: [],
  });
  const [selectedTag, setSelectedTag] = useState<TagTypesValues | null>(null);
  const [chipClickCount, setChipClickCount] = useState(0);

  useEffect(() => {
    const fetchData = async () => {
      try {
        if (
          tagSearchResponse.success &&
          tagSearchResponse.data &&
          tagSearchResponse.data.entries
        ) {
          const interventions: TagEntry[] = tagSearchResponse.data.entries
            .filter((entry: any) => entry.type === "FocusIntervention")
            .flatMap((entry: any) =>
              (entry.value || []).map((value: string, index: number) => ({
                id: `intervention-${index}`,
                value: value,
                label: (entry.label || [])[index],
              }))
            );

          const outcomes: TagEntry[] = tagSearchResponse.data.entries
            .filter((entry: any) => entry.type === "FocusOutcome")
            .flatMap((entry: any) =>
              (entry.value || []).map((value: string, index: number) => ({
                id: `outcome-${index}`,
                value: value,
                label: (entry.label || [])[index],
              }))
            );

          setDataTags({
            [tagTypes.Interventions]: interventions,
            [tagTypes.Outcomes]: outcomes,
          });
        } else {
          console.warn(
            "Unexpected response structure from /search/chips",
            tagSearchResponse
          );
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchData();
  }, []);

  const resetSelectedTag = () => {
    setSelectedTag(null);
  };

  const handleChipClick = (section: TagTypesValues) => {
    setSelectedTag(section || null);
    setChipClickCount((prevCount) => prevCount + 1);
  };

  const renderSectionContent = () => {
    return (
      <PromptSection
        centeredContent={true}
        handleChange={handleUserPrompt}
        queryLabelText={PROMPT_LABEL}
        isLoading={false}
        dataTags={dataTags}
        selectedTag={selectedTag}
        onCloseDropdown={resetSelectedTag}
        chipClickCount={chipClickCount}
      />
    );
  };

  return (
    <Box
      display="flex"
      flexDirection="column"
      mx="auto"
      className="chat-conversation"
      sx={{
        height: "calc(100vh - 160px)",
        overflowY: "hidden",
        transition: "padding-top 0.3s ease",
        width: isMobileOrTablet ? "100%" : "848px",
      }}
    >
      <Box
        className="chat-history-container"
        display="flex"
        flexDirection="column"
        flexGrow={1}
        px={0}
        height="100%"
        justifyContent="center"
        alignItems="center"
        position="relative"
      >
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          mx={isMobile ? "unset" : "auto"}
          pb={2}
        >
          <ConversationHeader isMobile={isMobile} />
        </Box>

        <Box width="100%" display="flex" justifyContent="center">
          {renderSectionContent()}
        </Box>

        <Box
          display="flex"
          justifyContent="center"
          gap={isMobileOrTablet ? 1 : 2}
          mt={isMobileOrTablet ? 1 : 0}
          sx={{
            flexDirection: { xs: "column", sm: "row" },
            alignItems: "center",
          }}
        >
          {tagList.map((tag) => {
            return (
              <Chip
                key={tag.label}
                label={tag.label}
                onClick={() => handleChipClick(tag.type)}
                color={tag.type === tagTypes.Outcomes ? "primary" : "default"}
                sx={{
                  backgroundColor: theme.elevation.paperElevationTwo,
                  color: theme.palette.text.primary,
                  height: "38px",
                  fontSize: "14px",
                  padding: "4px",
                  textTransform: "none",
                  width: { xs: "100%", sm: "auto" },
                  borderRadius: "100px",
                  "&:hover": {
                    backgroundColor: theme.palette.action.focus,
                    color: theme.palette.text.primary,
                  },
                  "&:focus": {
                    backgroundColor: theme.palette.action.focus,
                    color: theme.palette.text.primary,
                  },
                  "&:active": {
                    backgroundColor: theme.palette.action.focus,
                    color: theme.palette.text.primary,
                  },
                }}
              />
            );
          })}
        </Box>
      </Box>
    </Box>
  );
};

export default CenteredLandingPage;