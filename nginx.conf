events { }

http {
    server {
        listen 80;

        # location /api/ {
        #     rewrite /api/(.*) /$1  break;
        #     proxy_pass http://backend:8000;
        #     proxy_set_header Host $host;
        #     proxy_set_header X-Real-IP $remote_addr;
        #     proxy_buffering off;
        # }

        # location /openapi.json {
        #     rewrite /api/(.*) /$1  break;
        #     proxy_pass http://backend:8000;
        #     proxy_set_header Host $host;
        #     proxy_set_header X-Real-IP $remote_addr;
        #     proxy_buffering off;
        # }

        location / {
            proxy_pass http://frontend:3000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_buffering off;
        }
        add_header X-Frame-Options "DENY";
    }
}
