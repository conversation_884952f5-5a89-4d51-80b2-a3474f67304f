import React, { useEffect, useState } from "react";
import {
  Grid,
  Tabs,
  Tab,
  Box,
  Card,
  Typography,
  IconButton,
  Skeleton,
} from "@mui/material";
import ForestPlot from "../Plot/Graph/ForestPlot";
import StudiesRegionPlot from "../Plot/Graph/StudiesRegionPlot";
import SectorPlot from "../Plot/Graph/SectorPlot";
import { DownloadLinks, Source } from "../../../types/ConversationTypes";
import CloseIcon from "@mui/icons-material/Close";
import Sources from "../Sources/Sources";
import "./ComparisonView.css";
import ListSubheader from '@mui/material/ListSubheader';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
  theme: any;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, theme, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      {...other}
      style={{ flexGrow: 1, display: value === index ? 'flex' : 'none' }}
    >
      {value === index && (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            borderRadius: "8px",
            border: `1px solid ${theme.elevation.outlined}`,
            backgroundColor: `${theme.palette.background.default}`,
            overflow: "hidden",
            width: "100%",
            maxWidth: "100%",
            height: '100%',
            [theme.breakpoints.down("sm")]: {
              padding: "8px",
            },
          }}
        >
          <Box
            sx={{
              flexGrow: 1,
              overflowY: 'auto',
              p: 1,
              border: `1px solid ${theme.elevation.outlined}`,
              borderRadius: '8px',
            }}
          >
            {children}
          </Box>
        </Box>
      )}
    </div>
  );
}

interface ComparisonViewProps {
  informationId: string;
  plotDataInfo: any;
  sources: Source[] | undefined | null;
  selectedOutcome: string;
  selectedIntervention: string;
  theme: any;
  hoveredPair: { intervention: string; outcome: string } | undefined;
  onClose: () => void;
  activePlotCitationIds: string[];
  activePlotMessageId: string | null;
  activeSourcePaperIds: string[];
  activeSourceMessageId: string | null;
  selectedStudy: string;
}

const ComparisonView: React.FC<ComparisonViewProps> = ({
  informationId,
  plotDataInfo,
  sources,
  selectedOutcome,
  selectedIntervention,
  theme,
  hoveredPair,
  onClose,
  activePlotCitationIds,
  activePlotMessageId,
  activeSourcePaperIds,
  activeSourceMessageId,
  selectedStudy
}) => {
  const [plotLoading, setPlotLoading] = useState(false);
  const [plotError, setPlotError] = useState<string | null>(null);
  const [plotDataLabel, setPlotDataLabel] = useState<string | "">("");
  const [plotData, setPlotData] = useState<any>({});
  const [plotSectionId, setPlotSectionId] = useState<string | "">("");
  const [downloadLinks, setDownloadLinks] = useState<DownloadLinks | null>(
    null
  );
  const [value, setValue] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };
  console.log("PLOTDATA", plotData, plotDataInfo);

  const plotData2 = {
    ...plotData,
    flat_effect_sizes: plotData?.flat_effect_sizes?.map((d) => ({
      ...d,
      all_sectors: Array.from(
        new Set(
          [[...d.outcome_sectors.split(";")],
          [...d.intervention_sectors.split(";")]].flat()
        )
      ),
      intervention_tag_short_labels: d.intervention_tag_short_labels.split(","),
      intervention_sectors: d.intervention_sectors?.split(";"),
      intervention_tag_ids: d.intervention_tag_ids.split(",").map((d) => +d),
      outcome_tag_short_labels: d.outcome_tag_short_labels
        .split(",")[0]
        ?.split(":")[0],
      outcome_sectors: d.outcome_sectors.split(";"),
      outcome_tag_ids: d.outcome_tag_ids.split(",").map((d) => +d),
      paper_id: +d.paper_id,
      quality_score_category: d.quality_score_category.replace(/Very /g, ""),
      income_group:
        d.income_group !== null
          ? d.income_group
            ?.replace(/ economies/g, " ")
            .replace(/-/g, " ")
            .trim()
          : "N/A",
    })),
    interventions: plotData?.interventions?.map((d) => ({
      ...d,
      intervention_id: +d.intervention_id,
      intervention_sectors: Array.from(
        new Set(d.intervention_sectors.split(",").map((d) => d.split(":")[0]))
      ),
      intervention_tag_ids: d.intervention_tag_ids.split(",").map((d) => +d),
    })),
    outcomes: plotData?.outcomes
      ?.map((d) => ({
        ...d,
        outcome_id: +d.outcome_id,
        outcome_tag_ids: d.outcome_tag_ids.split(",").map((d) => +d),
        outcome_sectors: Array.from(
          new Set(d.outcome_sectors.split(",").map((d) => d.split(":")[0]))
        ),
      }))
      .sort((a, b) =>
        a.outcome_tag_short_labels.localeCompare(b.outcome_tag_short_labels)
      ),
    studies: plotData?.studies?.map((d) => ({
      ...d,
      id: +d.id,
      quality_score_group: d.quality_score_group.replace(/Very /g, ""),
    })),
  };

  console.log("PLOTDATA2", plotData2);

  useEffect(() => {
    if (plotDataInfo) {
      const plotDataToSet = plotDataInfo.data?.data || plotDataInfo.data;
      if (plotDataToSet) {
        setPlotData(plotDataToSet);
      }
      if (plotDataInfo.download_links) {
        setDownloadLinks(plotDataInfo.download_links);
      }
      if (plotDataInfo.title) {
        setPlotDataLabel(plotDataInfo.title);
      }
    }
    if (informationId) {
      setPlotSectionId(informationId);
    }
  }, [plotDataInfo, informationId]);

  if (
    (!plotLoading &&
      (!plotData ||
        (Array.isArray(plotData)
          ? plotData.length === 0
          : Object.keys(plotData).length === 0))) ||
    plotError
  ) {
    return null;
  }

  return (
    <Card
      elevation={0}
      sx={{
        width: "100%",
        height: "100%",
        border: `1px solid ${theme.components.input.outlined.disabledBorder}`,
        borderRadius: "8px",
        display: "flex",
        flexDirection: "column",
        background: theme.common.white.main,
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          p: 2,
          pb: 3,
          position: "sticky",
          top: 0,
          background: theme.common.white.main,
          zIndex: 1,
          flexShrink: 0,
        }}
      >
        <Typography
          variant="h6"
          component="div"
          sx={{ color: theme.palette.text.primary }}
        >
          Comparison view
        </Typography>
        <IconButton
          onClick={onClose}
          size="small"
          sx={{ color: theme.sidebar.secondaryFocused }}
        >
          <CloseIcon />
        </IconButton>
      </Box>

      <Box
        sx={{
          flexGrow: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
          px: 2,
          pt: 0,
          gap: 2,
        }}
      >
        <Box
          sx={{
            flex: "0 0 50%",
            minHeight: 0,
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Grid container spacing={3} sx={{ flexGrow: 1, height: '100%' }}>
            <Grid item xs={12} sm={6} sx={{ height: "100%" }}>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  height: "100%",
                  border: `1px solid ${theme.components.input.outlined.disabledBorder}`,
                  borderRadius: "8px",
                  p: 2,
                  overflow: "hidden",
                }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    mb: 2,
                    fontSize: "14px",
                    fontWeight: "500",
                    color: theme.palette.text.primary,
                    flexShrink: 0,
                  }}
                >
                  Intervention-outcome matrix
                </Typography>
                <Box sx={{ flexGrow: 1, overflowY: "auto", }}>
                  <Skeleton variant="rectangular" width="100%" height="100%" />
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6} sx={{ height: "100%" }}>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  height: "100%",
                  border: `1px solid ${theme.components.input.outlined.disabledBorder}`,
                  borderRadius: "8px",
                  overflow: "hidden",
                }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    p: 2,
                    pb: 0,
                    color: theme.palette.text.primary,
                    fontSize: "14px",
                    fontWeight: "500",
                  }}
                >
                  Forest plot
                </Typography>
                {plotData &&
                  (Array.isArray(plotData)
                    ? plotData.length > 0
                    : Object.keys(plotData).length > 0) && (
                    <Box
                      sx={{
                        flexGrow: 1,
                        display: "flex",
                        flexDirection: "column",
                        p: 2,
                        pt: 0,
                        overflow: "auto",
                      }}
                    >
                      <Tabs
                        value={value}
                        onChange={handleChange}
                        sx={{ mb: 2, flexShrink: 0 }}
                      >
                        <Tab label="Impact" value={0} style={{ textTransform: "none" }} />
                        <Tab label="Regions" value={1} style={{ textTransform: "none" }} />
                        <Tab label="Sectors" value={2} style={{ textTransform: "none" }} />
                      </Tabs>
                      <Box
                        id="data-visualization-tab-scroll"
                        sx={{
                          flexGrow: 1,
                          overflow: "auto",
                          display: "flex",
                          flexDirection: "column",
                        }}
                      >
                        <CustomTabPanel value={value} index={0} theme={theme}>
                          <ForestPlot
                            plotData={plotData2}
                            studiesData={plotData2?.studies}
                            plotLabel={plotDataLabel}
                            selectedOutcome={selectedOutcome}
                            selectedIntervention={selectedIntervention}
                            hoveredPair={hoveredPair}
                            activePlotCitationIds={activePlotCitationIds}
                            activePlotMessageId={activePlotMessageId}
                          />
                        </CustomTabPanel>
                        <CustomTabPanel value={value} index={1} theme={theme}>
                          <StudiesRegionPlot plotData={plotData2} />
                        </CustomTabPanel>
                        <CustomTabPanel value={value} index={2} theme={theme}>
                          <SectorPlot plotData={plotData2} />
                        </CustomTabPanel>
                      </Box>
                    </Box>
                  )}
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            flex: "0 0 50%",
            minHeight: 0,
            overflow: "hidden",
            display: 'flex',
            flexDirection: 'column',
            height: "100%",
            borderRadius: "8px",
            p: 0,
          }}
        >
          <Sources
            key={`sources-panel-${informationId}`}
            onClose={onClose}
            sources={sources}
            messageId={informationId}
            selectedStudy={selectedStudy}
            activeSourcePaperIds={activeSourcePaperIds}
            activeSourceMessageId={activeSourceMessageId}
            displayCloseButton={false}
            headerVariant={"body1"}
            headerFontSize={"14px"}
            headerFontWeight={"500"}
          />
        </Box>
      </Box>
    </Card>
  );
};
export default ComparisonView;