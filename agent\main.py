import structlog
import os
import logging
from typing import Optional
from contextlib import asynccontextmanager
from pydantic import BaseModel
from structlog.processors import TimeStamper
from fastapi import Fast<PERSON><PERSON>, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from src.services.agent import agent_service
from src.utils.db import engine

DEBUG = os.environ.get("DEBUG", True)

structlog.configure(
    processors=[
        TimeStamper(fmt="iso"),
        structlog.processors.JSONRenderer(),
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)

logger = logging.getLogger(__name__)


async def on_startup():
    logger.info("Starting agent service")
    engine.connect()


async def on_shutdown():
    logger.info("Shutting down agent service")


@asynccontextmanager
async def lifespan(_app: FastAPI):
    await on_startup()
    yield
    await on_shutdown()


app = FastAPI(debug=DEBUG, lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class AgentExecuteBody(BaseModel):
    conversation_id: Optional[str]
    query: str


@app.get("/")
def get_root():
    return {"success": True}


@app.post("/execute")
async def execute(body: AgentExecuteBody):
    conversation_id = body.conversation_id
    response = await agent_service.execute(conversation_id, body.query)

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={"response": response},
    )


@app.get("/health")
async def health_check():
    try:
        return JSONResponse(
            status_code=status.HTTP_200_OK, content={"status": "healthy!"}
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={"status": "unhealthy", "error": str(e)},
        )
