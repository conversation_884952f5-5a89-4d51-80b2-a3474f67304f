import structlog
import asyncio
from typing import Dict, Optional
from fastapi import WebSocket
from starlette.websockets import WebSocketState
from fastapi import WebSocket
from services.cache import (
    create_cache_key,
    get_redis_pubsub,
    get_redis_connection,
)
from models.socket import (
    AuthData,
    SubscriptionData,
    SendData,
    AuthType,
    ActiveConnection,
)

logger = structlog.get_logger()

pubsub = get_redis_pubsub()

REGISTERED_APPLICATIONS = {
    "agent": "tT6DeJcuXCrBkDak4yFziFmCP8",  # original agent
    "agent2": "tT6DeJcuXCrBkDak4yFziFmCP9",  # Second agent
    "agent3": "tT6DeJcuXCrBkDak4yFziFmCP0",  # Third agent
}

redis_client = get_redis_connection()


class SocketService:
    def __init__(self):
        self.active_connections: Dict[str, ActiveConnection] = {}
        asyncio.create_task(self.__redis_listener())

    async def connect(self, connection_id: str, socket: WebSocket):
        """Accept a new WebSocket connection."""
        await socket.accept()
        connection = ActiveConnection(socket=socket)
        await self.__set_connection_by_id(connection_id, connection)

    async def on_message(self, connection_id: str):
        """Handle incoming WebSocket messages."""
        active_connection = await self.__fetch_connection_by_id(connection_id)
        if (
            not active_connection
            or active_connection.socket.client_state != WebSocketState.CONNECTED
        ):
            await self.disconnect(connection_id)
            return  # Connection might have been closed

        try:
            message = await active_connection.receive_message()

            if message.action_is_auth():
                await self.handle_auth(connection_id, message.data)
            elif message.action_is_subscribe():
                await self.handle_subscribe(connection_id, message.data)
            elif message.action_is_unsubscribe():
                await self.handle_unsubscribe(connection_id, message.data)
            elif message.action_is_send():
                await self.handle_send(connection_id, message.data)
            else:
                await active_connection.send_error(
                    f"Unknown action type: {message.action}"
                )
        except Exception as e:
            logger.error(f"Error processing message for {connection_id}: {e}")
            await active_connection.send_error("Invalid message format.")
            await self.disconnect(connection_id)

    async def handle_auth(self, connection_id: str, data: AuthData):
        """Handle authentication requests."""
        active_connection = await self.__fetch_connection_by_id(connection_id)
        if not active_connection:
            return

        is_valid = False
        if data.type == AuthType.Application:
            if data.id not in REGISTERED_APPLICATIONS:
                await active_connection.send_error("Application not registered")
                return
            is_valid = data.token == REGISTERED_APPLICATIONS[data.id]
        else:
            is_valid = True  # Placeholder: Add user token validation logic here

        if is_valid:
            active_connection.update_auth(data)
            await self.__set_connection_by_id(connection_id, active_connection)
            await active_connection.send_success(
                f"Authenticated as {data.type}: {data.id}"
            )
        else:
            await active_connection.send_error("Authentication failed")

    async def handle_subscribe(self, connection_id: str, data: SubscriptionData):
        """Handle subscription requests."""
        active_connection = await self.__fetch_connection_by_id(connection_id)
        if not active_connection:
            return

        if not active_connection.is_authenticated():
            await active_connection.send_error("Not authenticated")
            return

        if active_connection.type_application():
            await active_connection.send_error(
                f"Only users can subscribe to {data.type}s"
            )
            return

        active_connection.subscribe(data)

        await self.__set_connection_by_id(connection_id, active_connection)

        subscription_key = self.__get_subscription_key(data.id)

        redis_client.sadd(subscription_key, connection_id)

        if subscription_key not in pubsub.channels:
            pubsub.subscribe(subscription_key)

        await active_connection.send_success(f"Subscribed to {data.type}: {data.id}")

    async def handle_unsubscribe(self, connection_id: str, data: SubscriptionData):
        """Handle unsubscribe requests."""
        active_connection = await self.__fetch_connection_by_id(connection_id)
        if not active_connection:
            return

        if not active_connection.is_authenticated():
            await active_connection.send_error("Not authenticated")
            return

        if active_connection.type_application():
            await active_connection.send_error(
                f"Only users can unsubscribe from {data.type}s"
            )
            return

        if not active_connection.is_subscribed(data):
            await active_connection.send_error(
                f"Not subscribed to {data.type}: {data.id}"
            )
            return

        active_connection.unsubscribe(data)
        await self.__set_connection_by_id(connection_id, active_connection)

        # Remove subscription from Redis set
        subscription_key = self.__get_subscription_key(data.id)
        redis_client.srem(subscription_key, connection_id)

        await active_connection.send_success(
            f"Unsubscribed from {data.type}: {data.id}"
        )

    async def handle_send(self, connection_id: str, data: SendData):
        """Handle message sending requests."""
        active_connection = await self.__fetch_connection_by_id(connection_id)
        if not active_connection:
            return

        if not active_connection.type_application():
            await active_connection.send_error("Only applications can send messages")
            return

        subscription_key = self.__get_subscription_key(data.subscription_id)
        subscriber_ids = redis_client.smembers(subscription_key)

        if not subscriber_ids:
            await active_connection.send_warning(
                f"No subscribers for subscription {data.subscription_id}"
            )
            return

        message = data.to_json()
        redis_client.publish(subscription_key, message)

        await active_connection.send_success(
            f"Message sent to subscription {data.subscription_id}"
        )

    async def disconnect(self, connection_id: str):
        """Handle WebSocket disconnection."""
        active_connection = await self.__fetch_connection_by_id(connection_id)
        if not active_connection:
            return

        # Remove all subscriptions for this connection from Redis
        for subscription in active_connection.subscribed_to:
            subscription_key = self.__get_subscription_key(subscription.id)
            redis_client.srem(subscription_key, connection_id)

        if active_connection.socket.client_state == WebSocketState.CONNECTED:
            await active_connection.socket.close(code=1000)
        await self.__delete_connection(connection_id)

    # Private methods

    def __get_connection_key(self, connection_id: str) -> str:
        return create_cache_key(prefix="active_connections", text=connection_id)

    def __get_subscription_key(self, subscription_id: str) -> str:
        return create_cache_key(prefix="connection_subscriptions", text=subscription_id)

    async def __redis_listener(self):
        """Listen to Redis Pub/Sub messages and forward to local WebSocket clients."""
        while True:
            message = pubsub.get_message(timeout=1.0)
            if message and message["type"] == "message":
                channel = message["channel"]
                data = message["data"]
                print(f"Received from Redis {channel}: {data}")

                # Find local subscribers
                subscriber_ids = redis_client.smembers(channel)
                for connection_id in subscriber_ids:
                    if connection := await self.__fetch_connection_by_id(connection_id):
                        try:
                            await connection.socket.send_text(data)
                        except Exception as e:
                            print(f"Error sending to {connection_id}: {e}")
                            await self.disconnect(connection_id)
            await asyncio.sleep(0.01)  # Prevent tight loop

    async def __fetch_connection_by_id(
        self, connection_id: str
    ) -> Optional[ActiveConnection]:
        cache_key = self.__get_connection_key(connection_id)
        return self.active_connections.get(cache_key)

    async def __set_connection_by_id(
        self, connection_id: str, connection: ActiveConnection
    ) -> None:
        cache_key = self.__get_connection_key(connection_id)
        self.active_connections[cache_key] = connection

    async def __delete_connection(self, connection_id: str) -> None:
        cache_key = self.__get_connection_key(connection_id)
        if cache_key in self.active_connections:
            del self.active_connections[cache_key]
