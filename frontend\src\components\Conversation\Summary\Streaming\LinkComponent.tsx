import React, { useRef, useState, useCallback } from "react";
import { Box, Tooltip, IconButton, Typography, Link } from "@mui/material";
import ArticleOutlinedIcon from '@mui/icons-material/ArticleOutlined';
import Counter from "./Counter";
import { useTheme } from '@mui/material/styles';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import ShowChartOutlinedIcon from '@mui/icons-material/ShowChartOutlined';
import CustomTooltip from '../../../Common/CustomTooltip';

interface LinkComponentProps {
  children: React.ReactNode;
  href?: string;
  messageId: string;
  onViewOnPlotClicked: (payload: { citation_ids: string[]; messageId: string }) => void;
  onViewOnPlotHover: (payload: {
    citation_ids: string[];
    messageId: string;
  }) => void;
  onViewOnSourceHover: (payload: {
    paper_ids: string[];
    messageId: string;
  }) => void;
  onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
}

const LinkComponent: React.FC<LinkComponentProps> = ({
  children,
  href,
  messageId,
  onViewOnPlotClicked,
  onViewOnPlotHover,
  onViewOnSourceHover,
  onViewOnSourceClicked,
}) => {
  const params = href ? new URLSearchParams(href) : null;

  const allPaperIds = params?.getAll("paper_id") || [];
  const allCitationIds = params?.getAll("citation_id") || [];

  const isOriginalSinglePaperLink = allPaperIds.length === 1 && params?.has("paper_position");

  const isNewStylePaperLink = allPaperIds.length > 0 && !params?.has("paper_position");

  const isCitationLink = allCitationIds.length > 0;
  const isPlotLink = href && !isNewStylePaperLink && !isOriginalSinglePaperLink && !isCitationLink;

  const isGraphLink = isCitationLink || isPlotLink;

  const originalSinglePaperId = isOriginalSinglePaperLink ? allPaperIds[0] : '';
  const originalSinglePaperDoiUrl = isOriginalSinglePaperLink ? (params?.get("paper_doi_url") || "") : "";
  const originalSinglePaperTitle = isOriginalSinglePaperLink ? (params?.get("paper_title") || "") : "";
  const originalSinglePaperCitation = isOriginalSinglePaperLink ? (params?.get("paper_citation") || "") : "";
  const originalSinglePaperPosition = isOriginalSinglePaperLink ? parseInt(params?.get("paper_position") || "1", 10) : 1;

  const [tooltipOpen, setTooltipOpen] = useState(false);
  const iconRef = useRef(null);
  const theme = useTheme();

  const generateLink = useCallback((url: string | undefined): string | undefined => {
    if (!url) return undefined;
    if (url.startsWith('http')) {
      return url;
    } else {
      return `https://doi.org/${url}`;
    }
  }, []);

  const handleTooltipOpen = (event: React.PointerEvent) => {
    setTooltipOpen(true);
    event.stopPropagation();
    if (isOriginalSinglePaperLink || isNewStylePaperLink) {
      onViewOnSourceHover({ paper_ids: allPaperIds, messageId });
    } else if (isCitationLink) {
      onViewOnPlotHover({ citation_ids: allCitationIds, messageId });
    }
  };

  const handleTooltipClose = (event: React.PointerEvent) => {
    setTooltipOpen(false);
    event.stopPropagation();
  };

  const handleIconClick = useCallback(() => {
    if (isOriginalSinglePaperLink && originalSinglePaperDoiUrl) {
      window.open(generateLink(originalSinglePaperDoiUrl), '_blank', 'noopener,noreferrer');
    } else if (isNewStylePaperLink) {
      onViewOnSourceClicked({ paper_ids: allPaperIds, messageId });
    } else if (isCitationLink) {
      onViewOnPlotClicked({ citation_ids: allCitationIds, messageId });
    }
  }, [
    isOriginalSinglePaperLink, originalSinglePaperDoiUrl, generateLink,
    isNewStylePaperLink, allPaperIds, messageId, onViewOnSourceClicked,
    isCitationLink, allCitationIds, onViewOnPlotClicked
  ]);

  const isAnySourceOrCitationLink = isOriginalSinglePaperLink || isNewStylePaperLink || isCitationLink;

  const commonIconStyles = {
    display: "flex",
    width: "16px",
    height: "24px",
    padding: "4px 1px",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    gap: "10px",
    flexShrink: 0,
    borderRadius: '100px',
    position: "relative",
    top: "-5px",
    mr: "2px",
    color: theme.components.icon.defaultLight,
    backgroundColor: theme.components.icon.backgroundDefaultFill,
    "&:hover": {
      backgroundColor: theme.components.icon.backgroundHover,
      color: theme.components.icon.defaultLight,
    },
    minWidth: "18px",
    minHeight: "18px",
  };

  const tooltipContent = isNewStylePaperLink ? (
    "View Sources"
  ) : isGraphLink ? (
    "View Graph"
  ) : isOriginalSinglePaperLink && originalSinglePaperDoiUrl ? (
    originalSinglePaperDoiUrl
  ) : (
    "No details available"
  );

  const renderTooltip = (
    isNewStylePaperLink || isGraphLink
  ) ? (
    <CustomTooltip
      content={tooltipContent}
      enterDelay={100}
      leaveDelay={300}
      open={tooltipOpen}
      onOpen={handleTooltipOpen}
      onClose={handleTooltipClose}
      placement="top"
    >
      <IconButton
        ref={iconRef}
        className="chart-icon"
        sx={{
          ...commonIconStyles,
          padding: isAnySourceOrCitationLink ? "0px" : "2px",
          cursor: 'pointer',
        }}
        onPointerEnter={handleTooltipOpen}
        onPointerLeave={handleTooltipClose}
        onClick={handleIconClick}
        disabled={false}
      >
        {isNewStylePaperLink ? (
          <ArticleOutlinedIcon sx={{ fontSize: 12 }} pointerEvents="none" />
        ) : isGraphLink ? (
          <ShowChartOutlinedIcon sx={{ fontSize: 12 }} pointerEvents="none" />
        ) : null}
      </IconButton>
    </CustomTooltip>
  ) : (
    <Tooltip
      interactive
      enterDelay={100}
      leaveDelay={300}
      open={tooltipOpen}
      onOpen={handleTooltipOpen}
      onClose={handleTooltipClose}
      title={
        <Box
          sx={{
            display: 'flex',
            padding: '10px 12px 12px 10px',
            flexDirection: 'column',
            alignItems: 'flex-start',
            gap: '10px',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <OpenInNewIcon sx={{ color: theme.palette.text?.disabled, fontSize: '14px' }} />
            {isOriginalSinglePaperLink && originalSinglePaperDoiUrl ? (
              <Link href={generateLink(originalSinglePaperDoiUrl)} target="_blank" rel="noopener noreferrer" sx={{ fontSize: '12px', color: theme.palette.text?.disabled, textDecoration: 'none' }}>
                {originalSinglePaperDoiUrl}
              </Link>
            ) : (
              <Typography sx={{ fontSize: '12px', color: theme.palette.text?.disabled }}>
                {"No details available"}
              </Typography>
            )}
          </Box>
          {isOriginalSinglePaperLink && originalSinglePaperTitle && <Typography sx={{ fontSize: '12px', color: theme.palette.text?.secondary }}>{originalSinglePaperTitle}</Typography>}
          {isOriginalSinglePaperLink && originalSinglePaperCitation && <Typography sx={{ fontSize: '12px', color: theme.palette.text?.secondary }}>{originalSinglePaperCitation}</Typography>}
        </Box>
      }
      placement="top"
      slotProps={{
        tooltip: {
          sx: {
            backgroundColor: theme.palette.background.paper,
            border: `0.75px solid ${theme.palette.components?.input?.outlined?.disabledBorder}`,
            boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.15)',
            borderRadius: '8px',
            padding: '0px',
          },
        },
      }}
    >
      <IconButton
        ref={iconRef}
        className="chart-icon"
        sx={{
          ...commonIconStyles,
          padding: isAnySourceOrCitationLink ? "0px" : "2px",
          cursor: (isOriginalSinglePaperLink && originalSinglePaperDoiUrl) ? 'pointer' : 'default',
        }}
        onPointerEnter={handleTooltipOpen}
        onPointerLeave={handleTooltipClose}
        onClick={handleIconClick}
        disabled={!(isOriginalSinglePaperLink && originalSinglePaperDoiUrl)}
      >
        {isOriginalSinglePaperLink ? (
          <Counter count={originalSinglePaperPosition} />
        ) : null}
      </IconButton>
    </Tooltip>
  );

  return (
    <Box component="span" sx={{ display: "inline-flex", alignItems: "center" }}>
      <Box
        component="span"
        sx={{
          display: "inline-block",
          verticalAlign: "middle",
          lineHeight: 1.4,
        }}
      >
        {children}
      </Box>
      <Box
        sx={{
          display: "inline-flex",
          justifyContent: "center",
          alignItems: "center",
          flexShrink: 0,
        }}
      >
        {renderTooltip}
      </Box>
    </Box>
  );
};

export default LinkComponent;