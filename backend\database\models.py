from sqlalchemy import Column, String, DateTime, Text, JSON, Foreign<PERSON><PERSON>, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy.orm import declarative_mixin
import uuid, json

Base = declarative_base()


@declarative_mixin
class TimestampMixin:
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    deleted_at = Column(DateTime, nullable=True)


class User(TimestampMixin, Base):
    """Represents a user in the system."""

    __tablename__ = "users"
    id = Column(
        String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4())
    )
    email = Column(String(255), unique=True, index=True)
    password_hash = Column(String(255), nullable=True)


class Conversation(TimestampMixin, Base):
    """Represents a conversation between users."""

    __tablename__ = "conversations"
    id = Column(
        String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4())
    )
    title = Column(String(255))
    user_id = Column(String(255), ForeignKey("users.id"))

    def display_title(self):
        """Return the title of the conversation."""
        return self.title if self.title else f"Chat started on {self.created_at}."


class Message(TimestampMixin, Base):
    """Represents a message in a conversation."""

    __tablename__ = "messages"
    id = Column(
        String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4())
    )
    conversation_id = Column(String(255), ForeignKey("conversations.id"))
    text = Column(Text)
    author = Column(String(255))
    type = Column(String(255))
    choices = Column(JSON, nullable=True)
    query_values = Column(JSON, nullable=True)
    streaming_finished_at = Column(DateTime, nullable=True)
    summary_configs = Column(JSON, nullable=True)


class Plot(TimestampMixin, Base):
    """Represents a plot associated with a message."""

    __tablename__ = "plots"
    id = Column(
        String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4())
    )
    message_id = Column(String(255), ForeignKey("messages.id"))
    title = Column(String(255))
    data = Column(JSON)

    def data_as_json(self):
        """Convert the plot data to JSON format."""
        return self.data if isinstance(self.data, dict) else json.loads(self.data)


class Source(TimestampMixin, Base):
    """Represents a source document related to a message."""

    __tablename__ = "sources"
    id = Column(
        String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4())
    )  # Set default to UUID
    message_id = Column(String(255), ForeignKey("messages.id"))
    paper_id = Column(String(255))
    position = Column(Integer, nullable=True)
    short_paper_id = Column(String(255), nullable=True)
    title = Column(String(255), nullable=True)
    doi_url = Column(String(255), nullable=True)
    journal_name = Column(String(255), nullable=True)
    citation = Column(JSON, nullable=True)


class Feedback(TimestampMixin, Base):
    """Represents feedback on a message."""

    __tablename__ = "feedbacks"
    id = Column(
        String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4())
    )
    message_id = Column(String(255), ForeignKey("messages.id"))
    reaction = Column(String(255))
    message = Column(Text())
    tags = Column(String(255))


class Waitlist(TimestampMixin, Base):
    """Represents users on waitlist."""

    __tablename__ = "waitlists"
    id = Column(
        String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4())
    )
    email = Column(String(255))
    organization = Column(String(255))
