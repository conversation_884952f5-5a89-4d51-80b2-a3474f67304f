import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    allowedHosts: [
      'localhost',
      'impact-ai-dev.app',
      'impact-ai-prod.app',
      'impactai-frontend-development-564807556547.us-east1.run.app',
    ],
    port: 3000,
    host: true,
  },
  plugins: [react()],
  build: {
    outDir: 'dist',
    minify: 'esbuild',
    sourcemap: false,
    target: 'esnext',
    rollupOptions: {
      input: {
        main: './index.html',
        index: './src/index.tsx',
      },
    },
  },
})
