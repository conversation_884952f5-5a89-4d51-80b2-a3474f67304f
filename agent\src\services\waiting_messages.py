"""Service for generating user-friendly waiting messages from agent logs."""

import logging
import asyncio
from pathlib import Path
from typing import List
from src.tools.llm_client import LLMClient
import os
import re
from dotenv import load_dotenv
import hashlib
import google.generativeai as genai

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


if "GOOGLE_API_KEY" in os.environ:
    genai.configure(api_key=os.environ["GOOGLE_API_KEY"])
    logger.info("Configured Google API key from environment variables")
else:
    logger.warning("GOOGLE_API_KEY not found in environment variables")


class WaitingMessageService:
    """Service that generates user-friendly waiting messages from agent logs."""

    def __init__(self, log_path: str = "logs/agent.log"):
        """Initialize the waiting message service.

        Args:
            log_path: Path to the agent log file
        """
        self.log_path = Path(log_path)
        self.llm_client = LLMClient(
            project_id=os.getenv("PROJECT_ID"),
            location=os.getenv("MYSQL_LOCATION"),
            model_name="gemini-2.0-flash-lite",
        )
        # Create a fallback status message in case we can't read logs
        self.current_status = "Processing your request..."
        self.last_processed_line = 0
        self.last_log_hash = None
        self.consecutive_unchanged_count = 0
        self.max_unchanged_iterations = 5  # Stop after 5 unchanged iterations

    def _extract_waiting_messages_logs(self) -> List[str]:
        """Extract only log lines that contain 'waiting_messages'."""
        try:
            with open(self.log_path, "r") as f:
                lines = f.readlines()
                waiting_lines = [line for line in lines if "waiting_messages" in line]

                # Check if logs have changed
                current_hash = self._get_hash(waiting_lines)

                if self.last_log_hash == current_hash:
                    self.consecutive_unchanged_count += 1
                    logger.info(
                        f"Logs unchanged, consecutive count: {self.consecutive_unchanged_count}"
                    )
                else:
                    self.consecutive_unchanged_count = 0
                    self.last_log_hash = current_hash

                # Update the last processed line count to avoid processing the same logs repeatedly
                self.last_processed_line = len(lines)
                return waiting_lines
        except Exception as e:
            logger.error(f"Error reading log file: {e}")
            return []

    def _get_hash(self, content):
        """Generate a hash for the content to check for changes."""
        if not content:
            return "empty"
        content_str = "".join(content)
        return hashlib.md5(content_str.encode()).hexdigest()

    def _extract_message_content(self, log_lines: List[str]) -> List[str]:
        """Extract the actual message content from log lines."""
        content_lines = []
        for line in log_lines:
            # The format is typically: timestamp - waiting_messages - INFO - ACTUAL_MESSAGE
            parts = line.split(" - ", 3)
            if len(parts) >= 4:
                content_lines.append(parts[3].strip())
        return content_lines

    async def generate_waiting_message(self) -> str:
        """Generate a user-friendly waiting message based on recent logs."""
        # Get log lines containing 'waiting_messages'
        waiting_logs = self._extract_waiting_messages_logs()

        if not waiting_logs:
            return self.current_status

        # Extract just the message content from the logs
        message_contents = self._extract_message_content(waiting_logs)

        if not message_contents:
            return self.current_status

        # Get the most recent activity (last 10 messages or all if less than 10)
        recent_activities = (
            message_contents[-10:] if len(message_contents) > 10 else message_contents
        )

        # Combine the logs into a structured format
        structured_logs = "\n".join(recent_activities)

        # Check if we should include a notice about processing potentially being complete
        completion_notice = ""
        if self.consecutive_unchanged_count >= 2:
            completion_notice = "\nNote: The system logs have not changed for a while, which might indicate the processing is complete or waiting for user action."

        # Prepare the prompt for the LLM
        prompt = f"""Based on these system logs, create a brief, user-friendly message (1-2 sentences)
        explaining what the system is currently doing in conversational language:

        {structured_logs}{completion_notice}

        The message should be:
        - Concise (maximum 1 sentence)
        - Written in present tense
        - Focused on what's currently happening
        - Do not mention any errors or issues that are present in the logs
        - Written in natural, conversational language (not technical)
        - Use Third Person like "Analyzing your request" or "Crafting your response"
        """

        try:
            response = await self.llm_client.generate(
                prompt, max_tokens=128, temperature=0.7
            )
            generated_message = response.text.strip()

            # Save the generated message
            self.current_status = generated_message
            return generated_message
        except Exception as e:
            logger.error(f"Error generating waiting message: {e}")

            # If we have recent activity but LLM fails, try to construct a basic message from the logs
            if recent_activities:
                # Look for patterns in the most recent logs
                last_msg = recent_activities[-1]

                if "ITERATION" in last_msg:
                    match = re.search(r"ITERATION (\d+)/(\d+)", last_msg)
                    if match:
                        iteration = match.group(1)
                        max_iter = match.group(2)
                        return f"Analyzing your request (step {iteration} of {max_iter})..."

                elif "ACTION: Executing tool" in last_msg:
                    tool_match = re.search(r"Executing tool (\w+)", last_msg)
                    if tool_match:
                        tool = tool_match.group(1)
                        return f"Gathering information using {tool}..."

                elif "TOOL_COMPLETE" in last_msg:
                    return "Processing information from data sources..."

                elif "FINAL ANSWER" in last_msg:
                    return "Finalizing your answer..."

                elif "DECISION: Generate final answer" in last_msg:
                    return "Crafting your response..."

            # Fallback to generic message
            return self.current_status

    async def start_message_updates(self, callback, interval: float = 5.0):
        """Start generating periodic waiting message updates.

        Args:
            callback: Function to call with new messages
            interval: Time between updates in seconds
        """
        try:
            while True:
                message = await self.generate_waiting_message()
                await callback(message)

                # Check if we should stop due to unchanged logs
                if self.consecutive_unchanged_count >= self.max_unchanged_iterations:
                    logger.info(
                        f"Stopping service due to {self.consecutive_unchanged_count} consecutive unchanged log iterations"
                    )
                    await callback(
                        f"Processing appears to be complete. No activity for {int(interval * self.consecutive_unchanged_count)} seconds."
                    )
                    break

                await asyncio.sleep(interval)
        except Exception as e:
            logger.error(f"Error in message update loop: {e}")
            raise


# Main function to run the waiting message service
async def message_callback(message: str):
    """Callback function that processes new waiting messages."""
    print(f"🔄 STATUS UPDATE: {message}")


async def run_waiting_message_service(
    log_path: str = "logs/agent.log", interval: float = 3.0
):
    """Run the waiting message service as a standalone application.

    Args:
        log_path: Path to the agent log file
        interval: Time between updates in seconds
    """
    print("🔍 Starting Waiting Message Service")
    print(f"📋 Monitoring log file: {log_path}")
    print(f"⏱️ Update interval: {interval} seconds")
    print("=" * 50)

    # Check for Google API key
    if "GOOGLE_API_KEY" not in os.environ:
        print("⚠️ WARNING: GOOGLE_API_KEY not found in environment variables")
        print("Set this environment variable or the service may not work correctly.")

    try:
        service = WaitingMessageService(log_path=log_path)
        await service.start_message_updates(message_callback, interval=interval)
        print("✅ Service completed - logs stopped changing")
    except KeyboardInterrupt:
        print("\n⏹️ Service stopped by user")
    except Exception as e:
        print(f"❌ Error running waiting message service: {e}")


# Command-line entry point
if __name__ == "__main__":
    import argparse

    # Set up command line arguments
    parser = argparse.ArgumentParser(
        description="Run the waiting message service to provide user-friendly status updates."
    )
    parser.add_argument(
        "--log-path", type=str, default="logs/agent.log", help="Path to agent log file"
    )
    parser.add_argument(
        "--interval", type=float, default=3.0, help="Update interval in seconds"
    )
    parser.add_argument(
        "--max-unchanged",
        type=int,
        default=5,
        help="Maximum number of iterations with unchanged logs before stopping",
    )
    args = parser.parse_args()

    # Configure basic logging for this script
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # Run the service
    try:
        service_coroutine = run_waiting_message_service(args.log_path, args.interval)
        asyncio.run(service_coroutine)
        print("Service completed successfully")
    except KeyboardInterrupt:
        print("\nService stopped")
