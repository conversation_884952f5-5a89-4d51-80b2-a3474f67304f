import structlog
from pydantic import BaseModel
from fastapi import APIRouter, Depends
from services.search import SearchService
from models.search import EntryType

router = APIRouter()
logger = structlog.get_logger()


class SearchTagsQueryParams(BaseModel):
    type: EntryType


search_service = SearchService()


@router.get("/search/chips")
async def get_search_chips():
    """Performs chip search."""
    try:
        results = await search_service.get_chips()
        return {"success": True, "data": {"entries": results}}
    except ValueError as e:
        logger.error("Failed to fetch entries.", error=e)
        return {"success": False, "error": "Failed to fetch entries."}


@router.get("/search/tags")
async def get_search_tags(query_params: SearchTagsQueryParams = Depends()):
    """Performs tag search. Specifying tag type as query param."""
    try:
        await search_service.initialize_entries()
        results = search_service.get_entries(query_params.type)
        entries = []
        for result in results:
            entries.append(
                {
                    "id": result["id"],
                    "label": result["tag_label"].capitalize(),
                    "value": result["tag_label"],
                }
            )
        return {"success": True, "data": {"entries": entries}}
    except ValueError as e:
        logger.error("Failed to fetch entries.", error=e)
        return {"success": False, "error": "Failed to fetch entries."}
