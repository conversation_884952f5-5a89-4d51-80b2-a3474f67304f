import React from "react";
import { Paper, Box, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import JoinWaitlistButton from "../components/JoinWaitlistButton";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";

const OurImpactSection = () => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();

    return (
        <Paper
            sx={{
                padding: "0px",
                width: "100%",
                height: "389px",
                boxShadow: 0,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                gap: isMobile ? "48px" : isTablet ? "56px" : "70px",
                borderRadius: "var(--4, 32px)",
                background: theme.palette.common.white,
            }}
        >
            {/* Header Section */}
            <Box
                sx={{
                    textAlign: "center",
                    display: "flex",
                    justifyContent: "center",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: "20px"
                }}>
                <Typography variant={isMobile ? "h3" : isTablet ? "h2" : "h2"}>
                    Our Impact
                </Typography>
                <Typography
                    variant={isMobile ? "body2" : isTablet ? "body2" : "body1"}
                    sx={{
                        width: isMobile ? "100%" : isTablet ? "100%" : "70%",
                        margin: 'auto',
                        textAlign: "center",
                        color: theme.palette.text.secondary,
                        fontSize: isMobile ? "12px !important" : isTablet ? "14px " : "16px"
                    }}
                >
                    ImpactAI breaks down barriers to high-quality research for development practitioners, policymakers, international organizations, researchers, and others passionate about social impact. It delivers practical solutions, best practices, and real-world evidence — enhancing decision-making, improving development projects, and creating efficiencies to help close financing gaps for sustainable development goals.
                </Typography>
                <JoinWaitlistButton />
            </Box>
        </Paper>
    );
};

export default OurImpactSection;
