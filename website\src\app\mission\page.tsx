"use client";
import { Box, Grid } from "@mui/material";
import Section1 from "./Section1";
import Section2 from "./Section2";
import OurImpactSection from "./OurImpactSection";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";
import DynamicPageTitle from '../components/DynamicPageTitle';

const Mission = () => {
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    return (
        <>
            <DynamicPageTitle
                title="Impact AI - Mission"
                description="ImpactAI is revolutionizing global development and empowering development practitioners with a GenAI-powered tool, delivering causal research insights in seconds."
            />
            <Box
                sx={{
                    width: "100%",
                    background: "none",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: 0,
                }}
            >
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "30px",
                        padding: 0,
                        width: "100%",
                        alignItems: "center",
                    }}
                >
                    <Grid container sx={{ mb: 0, p: 0 }}>
                        <Grid item xs={12} sx={{ p: 0, mt: isMobile ? "48px" : isTablet ? "90px" : "102px", }}>
                            <Section1 />
                        </Grid>
                        <Grid item xs={12}
                            sx={{
                                p: 0,
                                mt: isMobile ? "31.3px" : isTablet ? "70px" : "70px",
                            }}>
                            <Section2 />
                        </Grid>
                        <Grid item xs={12}
                            sx={{
                                p: 0,
                                mt: isMobile ? "31.3px" : isTablet ? "70px" : "70px",
                            }}>
                            <OurImpactSection />
                        </Grid>
                    </Grid>
                </Box>
            </Box>
        </>
    );
};

export default Mission;
