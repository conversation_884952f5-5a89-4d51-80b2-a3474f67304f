import React, { useState, useEffect, useRef } from "react";
import { Box, styled, useTheme, Theme } from "@mui/material";
import { LoadingState } from '../../LoadingState';

interface SystemLoadingIndicatorProps {
    isLoading: boolean;
}

const LoadingContainer = styled('div')(({ theme }: { theme: Theme }) => ({
    transition: theme.transitions.create('opacity', {
        duration: '0.3s',
        easing: 'ease-in-out',
    }),
    opacity: 0,
    '&.show': {
        opacity: 1,
    },
    width: '100%'
}));

const initialLoadingTexts: string[] = [
    "Analyzing the query",
    "Retrieving information",
    "Analyzing the results",
    "Generating the response",
    "Still thinking, hold tight"
];

const messageCycleMessages = initialLoadingTexts.slice(0, -1);
const finalMessage = initialLoadingTexts[initialLoadingTexts.length - 1];

const messageCycleDuration = 20000;
const messageCount = messageCycleMessages.length;
const messageDuration = messageCycleDuration / messageCount;

const dotInterval = 500;
const maxDots = 3;

const SystemLoadingIndicator: React.FC<SystemLoadingIndicatorProps> = ({ isLoading }) => {
    const theme = useTheme();
    const [displayedLoadingText, setDisplayedLoadingText] = useState<string | null>(null);

    const dotsRef = useRef<NodeJS.Timeout | null>(null);
    const startTimeRef = useRef<number>(0);
    const dotCountRef = useRef<number>(0);

    useEffect(() => {
        if (!isLoading) {
            if (dotsRef.current) clearInterval(dotsRef.current);
            setDisplayedLoadingText(null);
            return;
        }

        startTimeRef.current = Date.now();
        dotCountRef.current = 0;

        const updateText = () => {
            const elapsed = Date.now() - startTimeRef.current;
            let baseText: string;

            if (elapsed < messageCycleDuration) {
                const currentIndex = Math.floor(elapsed / messageDuration);
                baseText = messageCycleMessages[currentIndex];
            } else {
                baseText = finalMessage;
            }

            const dots = ".".repeat(dotCountRef.current);
            setDisplayedLoadingText(`${baseText}${dots}`);
        };

        updateText();

        dotsRef.current = setInterval(() => {
            dotCountRef.current = (dotCountRef.current + 1) % (maxDots + 1);
            updateText();
        }, dotInterval);

        return () => {
            if (dotsRef.current) clearInterval(dotsRef.current);
        };
    }, [isLoading]);

    if (!isLoading) {
        return null;
    }

    return (
        <LoadingContainer className={isLoading ? 'show' : ''}>
            <Box mt={0} display="flex" justifyContent="flex-start">
                <LoadingState
                    theme={theme}
                    layoutType='text'
                    loadingTexts={displayedLoadingText ? [displayedLoadingText] : undefined}
                    currentTextIndex={0}
                />
            </Box>
        </LoadingContainer>
    );
};

export default SystemLoadingIndicator;