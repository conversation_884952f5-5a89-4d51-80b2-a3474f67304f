"""RAG search module for development economics research documents."""

from typing import Dict, Any, List, Union
from pydantic import BaseModel
import logging
import json
from src.tools.base import Tool
from src.agent.utils import load_prompt
from src.tools.llm_client import LLMClient
from src.tools.sql_generator import QueryResult
import requests

logger = logging.getLogger(__name__)

# API Configuration
BASE_URL = "https://rag-qdrant-api-564807556547.us-central1.run.app/"


class RAGResults(BaseModel):
    """Results from RAG search."""

    response: str
    source_documents: List[str]
    metadata: Dict[str, Any] = {}

    def __str__(self) -> str:
        """String representation of the RAG results."""
        return (
            f"We now have the answer for the query after performing a semantic search.\n"
            f"Output:\nContext for the query (with sources between brackets): {self.response}\n"
        )

    async def get_response(
        self, query: str, llm: LLMClient, verbose: bool = False
    ) -> str:
        """Generate a response using the LLM based on retrieved passages."""
        if verbose:
            logger.info(f"Query: {query}")
            logger.info(f"Number of passages: {len(self.source_documents)}")
            logger.info(f"Passages: {self.response}")

        # Use load_prompt with context
        prompt = load_prompt(
            "rag_results", query=query, relevant_passages=self.response
        )

        if verbose:
            logger.info(f"Generated prompt:\n{prompt}")

        # Generate response using the LLM
        response = await llm.generate(prompt)
        return response.text


class RAGSearcher(Tool):
    """Tool for semantic search in research papers using external API."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the RAG searcher."""
        super().__init__(
            name="rag_search",
            description="Search within academic papers using semantic similarity to find relevant research discussions. You don't need to put the list of papers in your answer. You need to query for papers before using this tool. When using the query focus only on the concept economic concepts you are looking for.",
            func=self.search,
            arguments=[
                (
                    "query_result",
                    "QueryResult(user_query: str, dataset: str, row_count: int, unique_papers: int, paper_ids: Dict[str, Any], execution_time: float, metadata: Dict[str, Any])",
                ),
                ("num_results", "int = 15"),
            ],
            outputs=[
                (
                    "rag_results",
                    "RAGResults(response: str, source_documents: List[str], metadata: Dict[str, Any])",
                )
            ],
            config=config,
        )
        self.verbose = config.get("verbose", False)

    def _make_api_request(
        self, query: str, paper_ids: List[str], limit: int = 15
    ) -> Dict:
        """Make API request with proper error handling."""
        if not paper_ids:
            raise ValueError("No paper IDs provided for search")

        params = {"query": query, "limit": limit, "document_ids": ",".join(paper_ids)}

        if self.verbose:
            logger.info(f"Searching with parameters: {params}")

        try:
            response = requests.get(f"{BASE_URL}/search/", params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            if self.verbose:
                logger.error(f"API request failed: {e}")
            raise ValueError(f"External RAG API request failed: {e}")

    def _process_query_result(
        self, query_result: Union[QueryResult, Dict[str, Any], str]
    ) -> QueryResult:
        """Convert various input formats to QueryResult object."""
        if isinstance(query_result, QueryResult):
            return query_result

        if isinstance(query_result, str):
            try:
                query_result = json.loads(query_result)
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON string for query_result")

        return QueryResult(**query_result)

    def _build_rag_results(
        self, api_response: Dict, query_result: QueryResult
    ) -> RAGResults:
        """Build RAGResults from API response and query result."""
        results = api_response.get("results", [])

        # Format content and sources in one pass
        formatted_content = []
        sources = []

        for result in results:
            doc_id = result.get("document_id", "")
            if doc_id not in query_result.paper_ids:
                continue

            text = result.get("text", "")
            title = query_result.paper_ids.get(doc_id, "No title")
            header = result.get("metadata", {}).get("headers", {}).get("Header", "")

            # Format content
            content_header = (
                f"[Paper {doc_id} - {title}" + (f" - {header}" if header else "") + "]"
            )
            formatted_content.append(f"{content_header}\n{text}")

            # Add to sources
            sources.append(f"{doc_id}: {title}")

        content = "\n---\n".join(formatted_content)

        metadata = {
            "query": query_result.user_query,
            "embedding_model": api_response.get("embedding_model", ""),
            "total_found": api_response.get("total_found", 0),
            "time_taken": api_response.get("time_taken", 0),
            "paper_ids": list(query_result.paper_ids.keys()),
            "data_points": query_result.row_count,
        }

        return RAGResults(response=content, source_documents=sources, metadata=metadata)

    async def search(
        self,
        query_result: Union[QueryResult, Dict[str, Any], str],
        num_results: int = 15,
    ) -> RAGResults:
        """Search for relevant passages in papers using external API."""
        try:
            # Process and validate input
            processed_result = self._process_query_result(query_result)
            paper_ids = list(processed_result.paper_ids.keys())

            if self.verbose:
                logger.info(
                    f"Starting RAG search with query: {processed_result.user_query}"
                )
                logger.info(f"Number of papers: {processed_result.unique_papers}")

            # Make API call and build results
            api_response = self._make_api_request(
                processed_result.user_query, paper_ids, num_results
            )

            if self.verbose:
                logger.info(
                    f"API returned {len(api_response.get('results', []))} results"
                )

            return self._build_rag_results(api_response, processed_result)

        except Exception as e:
            if self.verbose:
                logger.error(f"Error performing RAG search: {str(e)}")
            raise
