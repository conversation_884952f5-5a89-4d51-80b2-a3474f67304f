import json
import os


class FileLoader:
    def __files_dir(self, path: str):
        current_dir = os.path.dirname(os.path.abspath(__file__))
        backend_dir = os.path.dirname(current_dir)
        return os.path.join(backend_dir, "files", path)

    def load_text(self, file_name: str):
        file_path = self.__files_dir(file_name)
        with open(file_path, "r") as file:
            content = file.read()
        return content

    def load_json(self, file_name: str):
        file_path = self.__files_dir(file_name)
        with open(file_path, "r") as file:
            content = file.read()
        return json.loads(content)


class FilesService(FileLoader):
    def load_forgot_passsword_email_template(self):
        file_name = "email-template-password-reset.html"
        return self.load_text(file_name=file_name)

    def load_tags_request(self):
        file_name = "fetch-tags-request.sql"
        return self.load_text(file_name=file_name)

    def load_agent_responses(self):
        file_name = "mock-agent-responses.json"
        return self.load_json(file_name=file_name)
