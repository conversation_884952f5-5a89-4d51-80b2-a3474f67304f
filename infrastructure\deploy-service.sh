#!/bin/bash

set -e

export FULL_SERVICE_NAME="impactai-$SERVICE-$TARGET"

source ./infrastructure/check-gcloud.sh
source ./infrastructure/check-env.sh

TIMESTAMP=$(date +%Y%m%d%H%M%S)
export IMAGE_TAG="gcr.io/$PROJECT_ID/$FULL_SERVICE_NAME:$TIMESTAMP"

echo "Building $FULL_SERVICE_NAME service image"

docker build -t $IMAGE_TAG ./${SERVICE}
docker push $IMAGE_TAG

envsubst < ./infrastructure/$SERVICE-service.yaml > ./infrastructure/$SERVICE-service-tmp.yaml

echo "Deploying service configs"
gcloud run services replace ./infrastructure/$SERVICE-service-tmp.yaml --region $REGION

echo "Deploying $FULL_SERVICE_NAME service"
gcloud run deploy $FULL_SERVICE_NAME --image $IMAGE_TAG --region $REGION \
  --platform managed \
  --allow-unauthenticated \
  --vpc-connector projects/impactai-430615/locations/us-east1/connectors/impact-ai-vpc-connector \
  --vpc-egress private-ranges-only \
  --project $PROJECT_ID

rm ./infrastructure/$SERVICE-service-tmp.yaml

# gcloud run services add-iam-policy-binding ${FULL_SERVICE_NAME} \
#     --region ${REGION} \
#     --member="allUsers" \
#     --role="roles/run.invoker"

echo "Deployment of $FULL_SERVICE_NAME completed successful with image tag: $IMAGE_TAG"
