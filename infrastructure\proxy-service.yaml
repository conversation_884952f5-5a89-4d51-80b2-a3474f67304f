apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: ${FULL_SERVICE_NAME}
  namespace: ${PROJECT_ID}
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/ingress-status: all
    autoscaling.knative.dev/target: "4"
    run.googleapis.com/minScale: "1"
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/vpc-access-connector: "projects/${PROJECT_ID}/locations/${REGION}/connectors/impact-ai-vpc-connector"
        run.googleapis.com/vpc-access-egress: "private-ranges-only"
    spec:
      containers:
        - image: "${IMAGE_TAG}"
          env:
            - name: PROJECT_ID
              value: "${PROJECT_ID}"
          ports:
            - containerPort: 8000
          resources:
            limits:
              memory: "8Gi"
              cpu: "4"
            requests:
              cpu: "2"
              memory: "8Gi"
          startupProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 5
            periodSeconds: 10
            failureThreshold: 5
      containerConcurrency: 10
      timeoutSeconds: 300
