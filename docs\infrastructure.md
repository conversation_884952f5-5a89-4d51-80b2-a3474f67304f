# Infrastructure and Deployment

This document describes the infrastructure setup and deployment configuration for the ImpactAI platform.

## Cloud Architecture

### Google Cloud Platform Components

1. **Cloud Run Services**
   - Backend API service
   - Agent service
   - Proxy service
   - Website service

2. **Load Balancing**
   - Global HTTPS load balancer
   - Cloud CDN integration
   - SSL certificate management
   - Static IP allocation

3. **Security**
   - Cloud Armor policies
   - VPC configuration
   - IAM roles and permissions
   - Service account management

4. **Storage**
   - Cloud Storage buckets
   - Static content delivery
   - Build artifacts storage

## Deployment Configuration

### Service Deployments

1. **Backend Service**
   ```yaml
   - Region: us-east1
   - Platform: Cloud Run managed
   - Memory: Variable allocation
   - VPC Connector: Enabled
   - Authentication: Public access
   ```

2. **Frontend Static Site**
   ```yaml
   - Bucket: GCS static hosting
   - CDN: Enabled
   - Cache Control: Optimized
   - SSL: Custom domain
   ```

3. **Agent Service**
   ```yaml
   - Region: us-east1
   - Memory: Configurable
   - Service Account: Custom compute SA
   - VPC Access: Private ranges
   ```

4. **Website Service**
   ```yaml
   - Memory: 512Mi
   - CPU: 1 core
   - Port: 3000
   - Environment: Configurable
   ```

### Load Balancer Setup

- Global HTTPS load balancer
- Serverless NEG configuration
- Cloud Armor security policy
- SSL certificate management
- Static IP allocation

## Deployment Process

### Automated Deployments

1. **Backend Deployment**
   ```bash
   make deploy-backend
   ```

2. **Frontend Deployment**
   ```bash
   make deploy-frontend
   ```

3. **Website Deployment**
   ```bash
   make deploy-website
   ```

4. **Agent Deployment**
   ```bash
   make deploy-agent
   ```

### Environment Configuration

- Development and production environments
- Secret management via Cloud Secret Manager
- Environment variable injection
- Service-specific configurations

### Security Measures

1. **Cloud Armor Rules**
   - IP range restrictions
   - DDoS protection
   - Custom security policies

2. **VPC Configuration**
   - Private connectivity
   - Egress control
   - Service isolation

3. **SSL/TLS**
   - Managed certificates
   - Custom domain support
   - Secure communication

## Monitoring and Maintenance

### Health Checks
- Automated service monitoring
- Load balancer health probes
- Error rate tracking
- Performance metrics

### Scaling
- Automatic scaling configuration
- Resource allocation
- Traffic management
- Load distribution

### Logging
- Centralized logging
- Error tracking
- Audit trails
- Performance monitoring
