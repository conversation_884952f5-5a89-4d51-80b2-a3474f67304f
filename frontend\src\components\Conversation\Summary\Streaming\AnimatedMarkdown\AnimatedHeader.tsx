import React, { useState, useRef, useEffect, useCallback } from 'react';
import { formatSummaryText } from "../Utils";

interface AnimatedHeaderProps {
  children: React.ReactNode;
  onComplete: () => void;
  as?: React.ElementType;
}

const AnimatedHeader = React.memo(({ children, onComplete, as = 'h3' }: AnimatedHeaderProps) => {
  const [content, setContent] = useState<string[]>([]);
  const totalWordsRef = useRef(0);
  const HeaderTag = as;
  const animationSpeed = 0.02;

  useEffect(() => {
    const headerText = formatSummaryText(children);
    const words = headerText.split(' ').filter(Boolean);
    setContent(words);
    totalWordsRef.current = words.length;
  }, [children]);

  const handleWordAnimated = useCallback((index: number) => {
    if (index === totalWordsRef.current - 1) {
      requestAnimationFrame(() => onComplete());
    }
  }, [onComplete]);

  return (
    <HeaderTag>
      {content.map((word, index) => (
        <span
          key={`<span class="math-inline">\{word\}\-</span>{index}`}
          className="animated-word"
          style={{ animationDelay: `${index * animationSpeed}s` }}
          onAnimationEnd={() => handleWordAnimated(index)}
        >
          {word}{' '}
        </span>
      ))}
    </HeaderTag>
  );
});

export default AnimatedHeader;