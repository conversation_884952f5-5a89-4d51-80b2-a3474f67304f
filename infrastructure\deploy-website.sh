#!/bin/bash

set -e

source ./infrastructure/check-gcloud.sh
source ./infrastructure/check-env.sh

PROJECT_ID="impactai-430615"
SERVICE_NAME="impactai-custom-site-$TARGET"
REGION="us-east1"
IMAGE_NAME="gcr.io/$PROJECT_ID/$SERVICE_NAME"


echo "Building and pushing Docker image..."
gcloud builds submit website --tag $IMAGE_NAME

echo "Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
  --image $IMAGE_NAME \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --memory 512Mi \
  --cpu 1 \
  --port 3000 \
  --set-env-vars "RECAPTCHA_API_KEY=$RECAPTCHA_API_KEY" \
  --set-env-vars "RECAPTCHA_SITE_KEY=$RECAPTCHA_SITE_KEY" \
  --set-env-vars "TARGET=$TARGET" \

echo "Deployment complete!"
echo "Service URL will be displayed above in the Cloud Run output"