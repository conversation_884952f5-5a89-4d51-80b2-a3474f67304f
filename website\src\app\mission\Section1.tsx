import React from "react";
import { Paper, Box, Grid, Typography, Card, CardContent, CardMedia } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";

const Section1 = () => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();

    const features = [
        {
            image: "/images/mission/problem.png",
            title: "The Challenge",
            alt: "Image illustrating challenges in research and data analysis",
            description: "Policymakers and researchers face obstacles in finding and interpreting the causal research they need. Traditional methods are slow, critical evidence is buried in extensive research, and normalizing results across sources often takes weeks of work."
        },
        {
            image: "/images/mission/solution.png",
            title: "Our Solution",
            alt: "ImpactAI tool interface summarizing research",
            description: "ImpactAI is an ensemble of custom large-language models that makes accessing impact evaluation research effortless: summarizing vast literature and highlighting key findings across topics and regions. By normalizing quantitative results from trusted sources, ImpactAI delivers accurate, context-specific insights for direct comparisons to identify the most impactful policy interventions - in seconds, not weeks."
        }
    ];

    return (
        <Paper
            sx={{
                padding: "0px",
                height: "auto",
                background: "none",
                boxShadow: 0,
                borderRadius: "8px",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: isMobile ? "47.5px" : isTablet ? "86px" : "143px",
            }}
        >
            {/* Header Section */}
            <Box sx={{
                textAlign: "center", width: isMobile ? "100%" : isTablet ? "80%" : "70%"
            }}>
                <Typography variant={isMobile ? "h1" : isTablet ? "h1" : "h1"} sx={{ pb: 1 }}>
                    More than a fruit basket of research - helping you compare apples to apples to identify impact.                </Typography>
                <Typography variant={isMobile ? "body2" : isTablet ? "body1" : "body1"} sx={{
                    textAlign: "center", color: `${theme.palette.primary.main} !important`,
                }}>
                    Designed for policymakers, researchers, and practitioners worldwide.
                </Typography>
            </Box>

            {/* Main Section */}
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    width: "100%",
                }}
            >
                <Grid container spacing={2} justifyContent="center">
                    {features.map((feature, index) => (
                        <Grid item xs={12} sm={6} md={6} key={index}>
                            <Card
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "stretch",
                                    borderRadius: "24px",
                                    p: '20px',
                                    boxShadow: 0,
                                    height: "100%",
                                    background: theme.palette.common.white,
                                }}
                            >
                                <CardMedia
                                    component="img"
                                    image={feature.image}
                                    alt={feature.alt}
                                    sx={{
                                        flex: 1,
                                        width: "100%",
                                        borderRadius: "12px",
                                        objectFit: "cover",
                                    }}
                                />

                                <CardContent
                                    sx={{
                                        flex: 1,
                                        display: "flex",
                                        flexDirection: "column",
                                        justifyContent: "flex-start",
                                        alignItems: "flex-start",
                                        gap: 2,
                                        px: 0,
                                        pb: '0px !important'
                                    }}
                                >
                                    <Typography
                                        variant="h4"
                                    >
                                        {feature.title}
                                    </Typography>
                                    <Typography
                                        variant="body1"
                                        sx={{ color: theme.palette.text.secondary }}
                                    >
                                        <span dangerouslySetInnerHTML={{ __html: feature.description }} />
                                    </Typography>
                                </CardContent>
                            </Card>
                        </Grid>
                    ))}
                </Grid>
            </Box>
        </Paper>
    );
};

export default Section1;
