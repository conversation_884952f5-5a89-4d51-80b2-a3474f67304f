"""Tool management module for the research agent."""

from typing import Dict, Any, List, Optional
from src.tools.base import Tool
from src.tools.entity_extractor import EntityExtractor, ExtractedEntities
from src.tools.sql_generator import SQLGenerator, QueryResult
from src.tools.rag_searcher import <PERSON><PERSON>earcher, RAGResults
from src.tools.structure_data_organizer import StructuredDataOrganizer, StructuredData
from src.tools.summary_generator import FinalAnswer, FinalAnswerGenerator
from src.tools.methodology_explainer import MethodologyExplainer
from src.utils.file_management import DatasetManager
from src.agent.config import DatasetSettings
import logging


logger = logging.getLogger(__name__)


class ToolCache:
    """Cache for tool outputs."""

    def __init__(self):
        """Initialize empty cache."""
        self.entities: Optional[ExtractedEntities] = None
        self.query_result: Optional[QueryResult] = None
        self.rag_results: Optional[RAGResults] = None
        self.structured_data: Optional[StructuredData] = None
        self.final_answer: Optional[FinalAnswer] = None
        self.dict_rows: Optional[Dict[str, Any]] = None
        self.url: Optional[str] = None
        dataset_settings = DatasetSettings()
        self.dataset_manager: Optional[DatasetManager] = DatasetManager(
            dataset_settings
        )

    def update_entities(self, entities: ExtractedEntities) -> None:
        """Update cached entities."""
        self.entities = entities

    def update_dict_rows(self, dict_rows: Dict[str, Any]) -> None:
        """Update cached dictionary containing a list of db result items."""
        self.dict_rows = dict_rows

    def update_query_result(self, query_result: QueryResult) -> None:
        """Update cached query result."""
        self.query_result = query_result

    def update_rag_results(self, rag_results: RAGResults) -> None:
        """Update cached RAG results."""
        self.rag_results = rag_results

    async def update_structured_data(self, structured_data: StructuredData) -> None:
        """Update cached structured data."""
        self.structured_data = structured_data
        self.url = structured_data.url
        if self.url:
            dict_rows = await self.dataset_manager.load_dataset(self.url)
            self.update_dict_rows(dict_rows)
            await self.dataset_manager._delete_blob_after_delay(
                self.url, delay_seconds=0
            )

    def update_final_answer(self, final_answer: FinalAnswer) -> None:
        """Update cached final answer"""
        self.final_answer = final_answer

    def update_studies_used(self, dict_rows: Dict[str, Any]) -> None:
        """Update cached entities."""
        self.dict_rows = dict_rows

    def clear(self) -> None:
        """Clear all cached data."""
        self.entities = None
        self.query_result = None
        self.rag_results = None
        self.structured_data = None
        self.final_answer = None
        self.dict_rows = None


class ToolManager:
    """Manages registration and access to agent tools."""

    def __init__(self, config: Dict[str, Any], session=None):
        """Initialize the tool manager with configuration."""
        self.tools = {}
        self.config = config
        self.session = session
        self._initialize_tools()

    def _initialize_tools(self):
        """Initialize all available tools."""
        # Initialize tool cache and aliases
        self.cache = ToolCache()
        self._tool_aliases = {
            "EntityExtractor": "entity_extractor",
            "SQLGenerator": "sql_generator",
            "RAGSearcher": "rag_search",
            "StructuredDataOrganizer": "structured_data_organizer",
            "FinalAnswerGenerator": "final_answer_generator",
            "MethodologyExplainer": "methodology_explainer",
        }

        # Initialize default tools
        self._register_default_tools()

    def _register_default_tools(self) -> None:
        """Register the default set of tools."""
        # Create a copy of the config to pass to tools, adding session if needed
        tool_config = (
            dict(self.config) if isinstance(self.config, dict) else self.config.dict()
        )

        # Add session to the tool config
        if self.session is not None:
            tool_config["session"] = self.session

        default_tools = {
            "entity_extractor": EntityExtractor(tool_config),
            "sql_generator": SQLGenerator(tool_config),
            "rag_search": RAGSearcher(tool_config),
            "structured_data_organizer": StructuredDataOrganizer(tool_config),
            "final_answer_generator": FinalAnswerGenerator(tool_config),
            "methodology_explainer": MethodologyExplainer(tool_config),
        }

        for name, tool in default_tools.items():
            self.register(name, tool)
            if tool_config.get("verbose", False):
                logger.info(f"Registered tool: {name}")

    def register(self, name: str, tool: Tool) -> None:
        """Register a new tool."""
        if name in self.tools:
            if self.config["verbose"]:
                logger.warning(f"Overwriting existing tool: {name}")
        self.tools[name] = tool

    def get_tool(self, name: str) -> Tool:
        """Get a registered tool by name."""
        # Check for alias
        tool_name = self._tool_aliases.get(name, name)
        if tool_name not in self.tools:
            available_tools = list(self.tools.keys()) + list(self._tool_aliases.keys())
            raise ValueError(
                f"Tool not found: {name}. Available tools: {available_tools}"
            )
        return self.tools[tool_name]

    def list_tools(self) -> List[Dict[str, Any]]:
        """List all registered tools with their metadata."""
        return [
            {
                "name": name,
                "description": tool.description,
                "arguments": tool.arguments,
                "outputs": tool.outputs,
            }
            for name, tool in self.tools.items()
        ]

    def _normalize_tool_name(self, tool_name: str) -> str:
        """Normalize tool names to registered versions."""
        name_map = {
            "rag_searcher": "rag_search",
        }
        return name_map.get(tool_name, tool_name)

    async def execute(self, tool_name: str, **kwargs) -> Any:
        """Execute a tool with given arguments."""
        tool = self.get_tool(tool_name)
        return await tool.func(**kwargs)

    async def execute_with_cache(self, tool_name: str, **kwargs) -> Any:
        """Execute a tool with caching logic."""
        # Normalize tool name
        tool_name = self._normalize_tool_name(tool_name)

        try:
            # Handle entity extraction
            if tool_name == "entity_extractor":
                result = await self.execute(tool_name, **kwargs)
                if isinstance(result, ExtractedEntities):
                    self.cache.update_entities(result)
                return result

            # Handle SQL generation
            if tool_name == "sql_generator":
                kwargs["entities"] = self.cache.entities
                result = await self.execute(tool_name, **kwargs)
                if isinstance(result, QueryResult):
                    self.cache.update_query_result(result)
                return result

            # Handle RAG search
            if tool_name == "rag_search":
                # Check if we have a cached query result
                if not self.cache.query_result:
                    raise ValueError(
                        "No SQL query results in cache. Please generate a SQL query first."
                    )

                kwargs["query_result"] = self.cache.query_result
                result = await self.execute(tool_name, **kwargs)
                if isinstance(result, RAGResults):
                    self.cache.update_rag_results(result)
                return result

            # Handle structured data generation
            if tool_name == "structured_data_organizer":
                # Use cached results if not provided
                if not self.cache.query_result:
                    raise ValueError(
                        "No SQL query results in cache. Please generate a SQL query first."
                    )

                kwargs["dataset"] = self.cache.query_result.dataset
                kwargs["entities"] = self.cache.entities
                result = await self.execute(tool_name, **kwargs)
                if isinstance(result, StructuredData):
                    await self.cache.update_structured_data(result)
                return result

            # handle summary generation
            if tool_name == "final_answer_generator":
                result = await self.execute(tool_name, **kwargs)
                if isinstance(result, FinalAnswer):
                    self.cache.update_final_answer(result)
                return result

            # Default execution for other tools
            return await self.execute(tool_name, **kwargs)

        except Exception as e:
            if self.config["verbose"]:
                logger.error(f"Error executing {tool_name}: {str(e)}")
            raise

    def clear_cache(self) -> None:
        """Clear all cached data."""
        self.cache.clear()
        if self.config["verbose"]:
            logger.info("Tool cache cleared")

    def get_tool_data(self) -> Dict[str, Any]:
        """Get tool data from cache."""
        return {
            "entities": self.cache.entities.dict() if self.cache.entities else None,
            "query_result": (
                self.cache.query_result.dict() if self.cache.query_result else None
            ),
            "rag_results": (
                self.cache.rag_results.dict() if self.cache.rag_results else None
            ),
            "structured_data": (
                self.cache.structured_data.dict()
                if self.cache.structured_data
                else None
            ),
            "dict_rows": self.cache.dict_rows or [],
            "final_answer": (
                self.cache.final_answer if self.cache.final_answer else None
            ),
        }
