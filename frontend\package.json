{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000 --host 0.0.0.0", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start": "vite"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@mui/icons-material": "^6.4.8", "@mui/lab": "^6.0.0-beta.31", "@mui/material": "^6.4.8", "@mui/material-nextjs": "^6.4.3", "axios": "^1.8.4", "d3": "^7.9.0", "framer-motion": "^12.5.0", "markdown-to-jsx": "^7.7.4", "ncu": "^0.2.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.4.0", "react-transition-group": "^4.4.5"}, "devDependencies": {"@types/express": "^5.0.1", "@types/json-server": "^0.14.7", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^8.27.0", "@typescript-eslint/parser": "^8.27.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.23.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "json-server": "^1.0.0-alpha.23", "redux": "^5.0.1", "typescript": "^5.8.2", "vite": "^6.2.6"}}