# ImpactAI Documentation

Welcome to the ImpactAI project documentation. This repository contains a comprehensive AI-powered platform for development economics research analysis and insights.

## Overview

ImpactAI is a specialized platform designed for development practitioners, policymakers, researchers, and international organizations. It provides research-backed insights using customized large language models (LLMs) and a structured knowledge database of validated research studies.

### Key Features

- AI-powered research analysis
- Interactive visualization of policy interventions
- Curated knowledge database of validated research
- Structured information on policy interventions and outcomes
- Quality-assessed research findings
- Bias mitigation through structured prompting

### Repository Structure

The repository is organized into several main components:

- [`backend/`](./backend.md) - Main application backend API
- [`agent/`](../docs/agent/README.md) - AI agent for processing and analyzing research papers
- [`frontend/`](./frontend.md) - Application frontend interface
- [`website/`](./website.md) - Landing page and marketing website
- [`infrastructure/`](./infrastructure.md) - Deployment and infrastructure configuration
- [`proxy/`](./proxy.md) - Proxy service configuration

## Getting Started

1. Install Docker following the [official installation guide](https://docs.docker.com/engine/install/)

2. Clone the repository:
   ```bash
   git clone https://github.com/worldbank/causal-ai-product.git
   cd causal-ai-product
   ```

3. Start the development environment:

- If you want to develop for the frontend
   ```bash
   make up
   ```
   You can also just `cd frontend` and `npm run dev` to start frontend development without docker

- If you want to develop for the backend
   ```bash
   make up-backend
   ```

- If you want to develop for the agent
   ```bash
   make up-agent
   ```

### Access Points

- Frontend: `http://localhost`
- Backend API: `http://localhost:8000`
- API Documentation: `http://localhost:8000/docs`

To stop the development environment:
```bash
make down
```

## Additional Documentation

- [Backend Documentation](./backend.md)
- [Agent Documentation](../docs/agent/README.md)
- [Frontend Documentation](./frontend.md)
- [Website Documentation](./website.md)
- [Infrastructure Documentation](./infrastructure.md)
- [Architecture Overview](./architecture.md)
- [Development Guide](./development.md)
