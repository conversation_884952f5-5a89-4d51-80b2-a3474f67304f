import uuid, json
import structlog
from utils.requests import post_json
from models.agent import AgentResponse
from utils.measure import measure_async_time
from services.files import FilesService
import google.generativeai as genai

logger = structlog.get_logger(__name__)

files_service = FilesService()
agent_responses = files_service.load_agent_responses()

hashed_responses = {}
for response in agent_responses:
    key = response["response"]["context"]["query"]
    hashed_responses[key] = response


class AgentService:
    """Service for summarizing data based on query values."""

    agent_api_url = "https://agent.impact-ai-dev.app"

    def __init__(self, conversation_id: uuid.UUID, query: str):
        self.conversation_id = conversation_id
        self.query = query

    @measure_async_time
    async def execute(self) -> AgentResponse:
        """Fetches summary configs based on the given tag."""
        api_url = f"{self.agent_api_url}/execute"

        response_data = None
        # if self.query in hashed_responses:
        #     response_data = hashed_responses[self.query]

        if not response_data:
            json_data = {
                "conversation_id": str(self.conversation_id),
                "query": self.query,
            }
            response_data = await post_json(api_url, body=json_data, timeout=300)
        try:
            return AgentResponse.from_json(json.dumps(response_data["response"]))
        except Exception as e:
            logger.error("Failed to get agent response.", error=e)
            raise e


async def get_gemini_completion(
    prompt: str, model_name: str = "gemini-1.5-flash"
) -> str:
    """Generates a completion from a Gemini model with error handling."""
    try:
        model = genai.GenerativeModel(model_name)
        response = await model.generate_content_async(prompt)
        return response.text
    except Exception as e:
        logger.error("Gemini API call failed", error=e)
        return ""
