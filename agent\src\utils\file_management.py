from google.cloud import storage
import json
import tempfile
import os
import asyncio
from typing import List, Dict, Any, Optional
import uuid
from src.agent.config import DatasetSettings
import time
from urllib.parse import urlparse


class DatasetManager:
    """Class for loading and saving datasets from/to GCP buckets."""

    def __init__(self, settings: DatasetSettings):
        """
        Initialize the DatasetManager.

        Args:
            bucket_name: Name of the GCP bucket
            credentials_path: Path to GCP credentials JSON file (optional)
        """
        if settings.credentials_path:
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = settings.credentials_path

        self.storage_client = storage.Client()
        self.bucket = self.storage_client.bucket(settings.bucket_name)

    async def load_dataset(self, blob_path: str) -> List[Dict[str, Any]]:
        """
        Load a dataset from a GCP bucket.

        Args:
            blob_path: Path to the blob in the bucket or full URL

        Returns:
            Dataset as a list of dictionaries
        """
        # If the blob_path includes the full URL, extract the correct path
        if "storage.googleapis.com" in blob_path:
            parsed_url = urlparse(blob_path)

            # For URLs like: https://storage.googleapis.com/impactai-tmp-agent-dataset/datasets/file.json
            # The path is like: /impactai-tmp-agent-dataset/datasets/file.json

            # Hardcode to datasets/ + filename since we know that's the structure
            if "datasets/" not in blob_path and ".json" in blob_path:
                filename = parsed_url.path.split("/")[-1].split("?")[0]
                blob_path = f"datasets/{filename}"
            else:
                # Try to extract path more carefully
                path_parts = parsed_url.path.split("/")
                # Find the index of 'datasets' in the path if it exists
                datasets_index = -1
                for i, part in enumerate(path_parts):
                    if part == "datasets":
                        datasets_index = i
                        break

                if datasets_index >= 0:
                    # Extract from 'datasets' onwards
                    rel_path = "/".join(path_parts[datasets_index:])
                    blob_path = rel_path.split("?")[0]
                else:
                    # Fallback to just using filename with datasets/ prefix
                    filename = parsed_url.path.split("/")[-1].split("?")[0]
                    blob_path = f"datasets/{filename}"

        blob = self.bucket.blob(blob_path)

        with tempfile.NamedTemporaryFile() as temp_file:
            blob.download_to_filename(temp_file.name)
            with open(temp_file.name, "r") as f:
                dataset = json.load(f)

        return dataset

    async def _delete_blob_after_delay(self, blob_path: str, delay_seconds: int = 300):
        """
        Delete a blob after a specified delay.

        Args:
            blob_path: Path to the blob in the bucket
            delay_seconds: Delay in seconds before deletion (default: 300 seconds = 5 minutes)
        """
        # Process the blob path if it's a full URL
        if "storage.googleapis.com" in blob_path:
            parsed_url = urlparse(blob_path)

            # For URLs like: https://storage.googleapis.com/impactai-tmp-agent-dataset/datasets/file.json

            # Hardcode to datasets/ + filename since we know that's the structure
            if "datasets/" not in blob_path and ".json" in blob_path:
                filename = parsed_url.path.split("/")[-1].split("?")[0]
                blob_path = f"datasets/{filename}"
            else:
                # Try to extract path more carefully
                path_parts = parsed_url.path.split("/")
                # Find the index of 'datasets' in the path if it exists
                datasets_index = -1
                for i, part in enumerate(path_parts):
                    if part == "datasets":
                        datasets_index = i
                        break

                if datasets_index >= 0:
                    # Extract from 'datasets' onwards
                    rel_path = "/".join(path_parts[datasets_index:])
                    blob_path = rel_path.split("?")[0]
                else:
                    # Fallback to just using filename with datasets/ prefix
                    filename = parsed_url.path.split("/")[-1].split("?")[0]
                    blob_path = f"datasets/{filename}"

        await asyncio.sleep(delay_seconds)
        try:
            blob = self.bucket.blob(blob_path)
            blob.delete()
            print(f"Deleted blob {blob_path} after {delay_seconds} seconds")
        except Exception as e:
            print(f"Error deleting blob {blob_path}: {str(e)}")

    async def save_dataset(
        self,
        dataset: List[Dict[str, Any]],
        blob_path: Optional[str] = None,
        expiration_seconds: int = 300,
    ) -> str:
        """
        Save a dataset to a GCP bucket with a time-to-live and return a signed URL.

        Args:
            dataset: Dataset as a list of dictionaries
            blob_path: Path to save the blob (optional, generates one if not provided)
            expiration_seconds: Time in seconds until both URL and blob expire (default: 300 seconds = 5 minutes)

        Returns:
            Signed URL to the saved dataset (valid for expiration_seconds)
        """
        if not blob_path:
            # Generate a unique path if none provided
            unique_id = str(uuid.uuid4())
            blob_path = f"datasets/{unique_id}.json"

        blob = self.bucket.blob(blob_path)

        # Set object metadata to expire in 5 minutes (300 seconds)
        current_time = int(time.time())
        expires_at = current_time + expiration_seconds
        blob.metadata = {"ttl": str(expires_at)}

        # Set cache control header
        blob.cache_control = f"private, max-age={expiration_seconds}"
        blob.content_type = "application/json"

        with tempfile.NamedTemporaryFile(mode="w+", delete=False) as temp_file:
            json.dump(dataset, temp_file)
            temp_file_path = temp_file.name

        try:
            # Upload the file
            blob.upload_from_filename(temp_file_path)
        finally:
            # Clean up the temporary file
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

        # Generate a signed URL that expires after specified time
        url = blob.generate_signed_url(expiration=expiration_seconds)

        return url
