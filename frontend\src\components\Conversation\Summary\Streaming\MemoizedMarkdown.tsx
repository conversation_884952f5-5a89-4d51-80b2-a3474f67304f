import React, { useCallback, useRef, useEffect, useMemo, useState } from 'react';
import Markdown from 'markdown-to-jsx';
import { renderTextWithTags } from "./Utils";
import AnimatedHeader from "./AnimatedMarkdown/AnimatedHeader";
import AnimatedParagraph from "./AnimatedMarkdown/AnimatedParagraph";
import AnimatedList from "./AnimatedMarkdown/AnimatedList";
import AnimatedEm from "./AnimatedMarkdown/AnimatedEm";

interface MemoizedMarkdownProps {
  text: string;
  onComplete: () => void;
  sources?: any;
  plotData?: any;
  onViewOnPlotClicked: (payload: { citation_ids: string[]; messageId: string }) => void;
  onViewOnPlotHover: (payload: { citation_ids: string[]; messageId: string }) => void;
  onViewOnSourceHover: (payload: { paper_ids: string[]; messageId: string }) => void;
  onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
  messageId?: string;
}

const MemoizedMarkdown = React.memo(({
  text,
  onComplete,
  sources,
  plotData,
  onViewOnPlotClicked,
  onViewOnPlotHover,
  onViewOnSourceHover,
  onViewOnSourceClicked,
  messageId,
}: MemoizedMarkdownProps) => {
  const [isAnimationComplete, setIsAnimationComplete] = useState(false);
  const handleAnimationComplete = useCallback(() => {
    if (!isAnimationComplete) {
      setIsAnimationComplete(true);
      onComplete();
    }
  }, [onComplete, isAnimationComplete]);

  const headerLevels = ['h1', 'h2', 'h3', 'h4', 'h5'] as const;
  type HeaderLevel = typeof headerLevels[number];
  const markdownOptions = useMemo(() => ({
    overrides: {
      ul: {
        component: (props: any) => (
          <ul start={props.start} className="streaming">
            <AnimatedList {...props} onComplete={handleAnimationComplete} />
          </ul>
        ),
      },
      ol: {
        component: (props: any) => (
          <ol start={props.start} className="streaming">
            <AnimatedList {...props} onComplete={handleAnimationComplete} />
          </ol>
        ),
      },
      li: {
        component: (props: any) => (
          <AnimatedParagraph
            {...props}
            onComplete={handleAnimationComplete}
            messageId={messageId}
            onViewOnPlotClicked={onViewOnPlotClicked}
            onViewOnPlotHover={onViewOnPlotHover}
            onViewOnSourceHover={onViewOnSourceHover}
            onViewOnSourceClicked={onViewOnSourceClicked}
          />
        )
      },
      ...headerLevels.reduce((acc, level) => {
        acc[level as HeaderLevel] = {
          component: (props: any) => (
            <AnimatedHeader
              key={props.key}
              {...props}
              onComplete={handleAnimationComplete}
              as={level}
            />
          ),
        };
        return acc;
      }, {} as Record<HeaderLevel, any>),
      p: {
        component: (props: any) => (
          <AnimatedParagraph
            {...props}
            onComplete={handleAnimationComplete}
            messageId={messageId}
            onViewOnPlotClicked={onViewOnPlotClicked}
            onViewOnPlotHover={onViewOnPlotHover}
            onViewOnSourceHover={onViewOnSourceHover}
            onViewOnSourceClicked={onViewOnSourceClicked}
          />
        ),
      },
      em: {
        component: AnimatedEm
      },
    },
  }), [handleAnimationComplete, messageId, onViewOnPlotClicked, onViewOnPlotHover, onViewOnSourceHover, onViewOnSourceClicked]);

  return (
    <Markdown
      className="streaming animating"
      options={markdownOptions}
    >
      {renderTextWithTags(text, sources, plotData)}
    </Markdown>
  );
}, (prevProps, nextProps) => {
  return prevProps.text === nextProps.text;
});

export default MemoizedMarkdown;