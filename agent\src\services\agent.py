from src.agent.main import Agent
from src.services.websocket_service import socket_service


class AgentService:
    def __init__(self):
        self.configs = {
            "model_name": "gemini-2.0-flash-001",
            "temperature": 0.1,
            "max_tokens": 8192,
            "max_iterations": 10,
            "verbose": True,
            "bucket_name": "scihub-papers-processed",
            "use_gcp": True,
        }

    async def execute(self, conversation_id: str, query: str):
        self.agent = Agent(
            self.configs,
            socket_service=socket_service,
            conversation_id=conversation_id,
        )
        response = await self.agent.execute(query=query)
        tool_data = self.agent.tool_manager.get_tool_data()

        dict_rows = tool_data.get("dict_rows")

        return {
            "response": response,
            "context": {
                "query": query,
                "conversation_id": self.agent.conversation_id,
                "tool_data": {
                    "data_used": dict_rows or [],
                },
            },
        }


agent_service = AgentService()
