import random
from services.files import FilesService
from models.search import EntryType
from sqlalchemy import text
from utils.requests import post_json
from typing import Any
from database.core import get_session as get_core_db_session


class SearchService:
    def __init__(self) -> None:
        self.items_per_page = 5
        self.outcome_tags: list[Any] = []
        self.intervention_tags: list[Any] = []
        self.files_service = FilesService()
        self.initialized = False

    async def initialize_entries(self):
        if not self.initialized:
            self.intervention_tags = await self.__fetch_top_tags_by_type("intervention")
            self.outcome_tags = await self.__fetch_top_tags_by_type("outcome")
        self.initialized = True

    async def get_chips(self) -> list[Any]:
        response = await post_json(
            url="https://chips-din4qs2qka-uc.a.run.app/get-chips/", body="{}"
        )
        chips = []
        for key in response.keys():
            chips.append(
                {
                    "type": key,
                    "value": response[key],
                    "label": response[key],
                }
            )
        return chips

    def get_entries(self, type: EntryType) -> list[Any]:
        if type == "interventions":
            return self.__get_interventions()
        return self.__get_outcomes()

    def __get_interventions(self) -> list[Any]:
        if len(self.intervention_tags) <= 0:
            return []
        return random.sample(self.intervention_tags, self.items_per_page)

    def __get_outcomes(self) -> list[Any]:
        if len(self.outcome_tags) <= 0:
            return []
        return random.sample(self.outcome_tags, self.items_per_page)

    async def __fetch_top_tags_by_type(self, type: EntryType) -> list[Any]:
        async with get_core_db_session() as session:
            query = self.files_service.load_tags_request()
            query = query.replace(":tag_type", type)
            query = text(query)
            result = await session.execute(query)
            return [row._asdict() for row in result.all()]
