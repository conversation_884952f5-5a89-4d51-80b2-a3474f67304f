import { useState, useEffect } from "react";
import { get } from "../services/apiService";
import {
  Source,
  Message,
  Option
} from "../types/ConversationTypes";
import { API_ERROR_MESSAGE } from "../utils/labels";

interface PlotAndSourcesData {
  plotData: any | null;
  sourcesData: Source[] | null;
  text: string | null;
  choicesData: Option[] | null;
  fullMessageInfo: Message | null;
  hasAttemptedGetInfoFetch: boolean;
}

interface UsePlotAndSourcesDataProps {
  conversationId: string;
  informationMessageId: string;
  hasStreamingStartedForDataFetch: boolean;
}

const usePlotAndSourcesData = ({
  conversationId,
  informationMessageId,
  hasStreamingStartedForDataFetch,
}: UsePlotAndSourcesDataProps): PlotAndSourcesData => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [plotData, setPlotData] = useState<any | null>(null);
  const [sourcesData, setSourcesData] = useState<Source[] | null>(null);
  const [text, setText] = useState<string | null>(null);
  const [choicesData, setChoicesData] = useState<Option[] | null>(null);
  const [fullMessageInfo, setFullMessageInfo] = useState<Message | null>(null);
  const [hasAttemptedGetInfoFetch, setHasAttemptedGetInfoFetch] = useState(false);

  const getMessageInfo = async (
    currentConversationId: string,
    messageId: string
  ) => {
    setLoading(true);
    setError(null);
    setSourcesData([]);
    setPlotData(null);
    setText(null);
    setChoicesData(null);

    try {
      const response: any = await get(
        `/conversations/${currentConversationId}/messages/${messageId}`
      );
      if (response?.success) {
        setSourcesData(response.data?.sources || []);
        setPlotData(response.data?.plot || null);
        setText(response.data?.text || null);
        setChoicesData(response.data?.choices || null);
        setFullMessageInfo(response.data as Message);
        setHasAttemptedGetInfoFetch(true);
      } else {
        setError(API_ERROR_MESSAGE);
        setHasAttemptedGetInfoFetch(true);
      }
    } catch (err) {
      console.error("getMessageInfo error:", err);
      setError(API_ERROR_MESSAGE);
      setHasAttemptedGetInfoFetch(true);
    } finally {
      setLoading(false);
      setHasAttemptedGetInfoFetch(true);
    }
  };

  useEffect(() => {
    if (conversationId && informationMessageId && hasStreamingStartedForDataFetch) {
      getMessageInfo(conversationId, informationMessageId);
    }
  }, [conversationId, informationMessageId, hasStreamingStartedForDataFetch]);

  return { plotData, sourcesData, text, choicesData, fullMessageInfo, hasAttemptedGetInfoFetch }
};

export default usePlotAndSourcesData;