import json
import time
import uuid
import os
import asyncio
from typing import List, Dict, Any, Counter
import logging
import dotenv
from datetime import datetime
import statistics
from src.agent.main import Agent
from src.tools.llm_client import LLMJudge
import aiohttp

from src.services.websocket_service import SocketService

dotenv.load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set Google Application Credentials
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "data/impactai-430615-990127f0162f.json"

AGENT_SOCKET_ID = "agent"
AGENT_SOCKET_SECRET = "tT6DeJcuXCrBkDak4yFziFmCP8"
WEBSOCKET_URI = "wss://api.impact-ai-dev.app"


class AgentAnalytics:
    """
    Service for running analytics on the agent by testing it against a set of questions
    and saving the results for further analysis.
    """

    def __init__(self, analytics_dir: str = "data/analytics"):
        """
        Initialize the analytics service.

        Args:
            analytics_dir: Directory to save the results
        """
        self.analytics_dir = analytics_dir
        os.makedirs(analytics_dir, exist_ok=True)

    def load_questions(self, questions_file: str = "questions.json") -> List[str]:
        """
        Load questions from a JSON file.

        Args:
            questions_file: File containing the questions

        Returns:
            List of questions
        """
        try:
            filepath = os.path.join(self.analytics_dir, questions_file)
            if os.path.exists(filepath):
                with open(filepath, "r") as f:
                    return json.load(f)
            else:
                logger.warning(f"Questions file not found: {filepath}")
                return []
        except Exception as e:
            logger.error(f"Error loading questions: {e}")
            return []

    def save_questions(
        self, questions: List[str], questions_file: str = "questions.json"
    ) -> None:
        """
        Save questions to a JSON file.

        Args:
            questions: List of questions to save
            questions_file: File to save the questions
        """
        filepath = os.path.join(self.analytics_dir, questions_file)
        with open(filepath, "w") as f:
            json.dump(questions, f, indent=4)
        logger.info(f"Questions saved to {filepath}")

    async def run_agent_on_questions(
        self,
        questions: List[str],
        output_file: str = "agent_results.json",
        force: bool = False,
    ) -> List[Dict[str, Any]]:
        """
        Run the agent on a list of questions sequentially.
        If results already exist and force=False, it will load the existing results.

        Args:
            questions: List of questions to test
            output_file: File to save the results
            force: Whether to force re-running even if results exist

        Returns:
            List of dictionaries containing the results
        """
        # Check if results already exist
        summary_output_path = os.path.join(self.analytics_dir, output_file)
        detailed_output_file = output_file.replace(".json", "_detailed.json")

        if os.path.exists(summary_output_path) and not force:
            logger.info(
                f"Results already exist at {summary_output_path}. Loading existing results."
            )
            with open(summary_output_path, "r") as f:
                return json.load(f)

        summary_results = []
        detailed_results = []
        total_questions = len(questions)

        logger.info(f"Running agent on {total_questions} questions sequentially...")

        # Process questions one by one
        for i, question in enumerate(questions):
            logger.info(
                f"Processing question {i + 1}/{total_questions}: {question[:50]}..."
            )

            # Create a new session for each question
            async with aiohttp.ClientSession() as session:
                # Configure agent
                agent_config = {
                    "model_name": "gemini-2.0-flash-001",
                    "temperature": 0.1,
                    "max_tokens": 8192,
                    "max_iterations": 10,
                    "verbose": True,
                    "bucket_name": "scihub-papers-processed",
                    "use_gcp": True,
                    "google_api_key": os.environ.get("GOOGLE_API_KEY"),
                    "shared_session": session,
                    "conversation_id": str(uuid.uuid4()),
                }

                conversation_id = str(uuid.uuid4())
                agent = None
                socket_service = None

                try:
                    # Initialize socket service
                    logger.info("Initializing socket service...")
                    socket_service = SocketService(
                        AGENT_SOCKET_ID, AGENT_SOCKET_SECRET, WEBSOCKET_URI
                    )

                    # Create agent with the new session
                    logger.info("Creating agent...")
                    start_time = time.time()
                    agent = Agent(
                        agent_config,
                        socket_service=socket_service,
                        conversation_id=conversation_id,
                    )
                    logger.info("Agent created successfully")

                    # Run the agent on the question
                    logger.info(f"Executing agent with query: {question}")
                    agent_response = await agent.execute(query=question)
                    end_time = time.time()
                    total_execution_time = end_time - start_time

                    logger.info(f"Agent response time: {total_execution_time} seconds")
                    logger.info(f"Agent response: {agent_response}")

                    # Get pipeline metrics and tool data
                    pipeline_metrics = (
                        agent.pipeline_manager.get_metrics_summary()
                        if hasattr(agent, "pipeline_manager")
                        else {}
                    )
                    tool_data = agent.tool_manager.get_tool_data()

                    # 1. SUMMARY RESULT - High-level overview
                    summary_result = self._create_summary_result(
                        question=question,
                        agent_response=agent_response,
                        agent=agent,
                        conversation_id=conversation_id,
                        total_execution_time=total_execution_time,
                        pipeline_metrics=pipeline_metrics,
                    )

                    # 2. DETAILED RESULT - Step-by-step pipeline details
                    detailed_result = self._create_detailed_result(
                        question=question,
                        agent_response=agent_response,
                        agent=agent,
                        conversation_id=conversation_id,
                        tool_data=tool_data,
                        pipeline_metrics=pipeline_metrics,
                    )

                except Exception as e:
                    logger.error(
                        f"Error processing question {i + 1}: {e}", exc_info=True
                    )

                    # Create error results for both types
                    summary_result = self._create_error_summary_result(
                        question, conversation_id, e, locals()
                    )
                    detailed_result = self._create_error_detailed_result(
                        question, conversation_id, e, locals()
                    )

                finally:
                    # Clean up agent resources
                    logger.info("Cleaning up resources...")
                    if agent is not None:
                        try:
                            await agent.cleanup()
                            logger.info("Agent cleaned up")
                        except Exception as ae:
                            logger.error(f"Error cleaning up agent: {ae}")

                # Add results
                summary_results.append(summary_result)
                detailed_results.append(detailed_result)

                # Save after each question for resilience
                self.save_results(summary_results, output_file)
                self.save_results(detailed_results, detailed_output_file)

                # Add a pause between questions
                await asyncio.sleep(2)

        return summary_results

    def _create_summary_result(
        self,
        question: str,
        agent_response: str,
        agent,
        conversation_id: str,
        total_execution_time: float,
        pipeline_metrics: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Create summary result with high-level information."""

        # Extract thoughts and actions from LLM outputs
        thoughts = []
        actions = []
        pipelines_executed = []

        if hasattr(agent, "state") and agent.state.llm_outputs:
            for llm_output in agent.state.llm_outputs:
                if (
                    hasattr(llm_output, "parsed_response")
                    and llm_output.parsed_response
                ):
                    thoughts.append(
                        {
                            "iteration": llm_output.iteration,
                            "thought": llm_output.parsed_response.thought,
                            "intent": llm_output.intent,
                        }
                    )

                    if llm_output.parsed_response.action:
                        action_info = {
                            "iteration": llm_output.iteration,
                            "pipeline_name": llm_output.parsed_response.action.get(
                                "name"
                            ),
                            "reason": llm_output.parsed_response.action.get("reason"),
                            "execution_time": llm_output.action_time_seconds,
                        }
                        actions.append(action_info)
                        pipelines_executed.append(
                            llm_output.parsed_response.action.get("name")
                        )

        # Get pipeline step timings from metrics
        pipeline_step_times = {}
        failures = []

        if pipeline_metrics and "pipelines" in pipeline_metrics:
            for pipeline_data in pipeline_metrics["pipelines"]:
                pipeline_name = pipeline_data.get("pipeline_name")
                if pipeline_name:
                    pipeline_step_times[pipeline_name] = {
                        "total_duration": pipeline_data.get("total_duration"),
                        "success": pipeline_data.get("success"),
                        "steps": [
                            {
                                "step_name": step.get("step_name"),
                                "duration": step.get("duration"),
                                "success": step.get("success"),
                            }
                            for step in pipeline_data.get("steps", [])
                        ],
                    }

                    if not pipeline_data.get("success"):
                        failures.append(
                            {
                                "pipeline": pipeline_name,
                                "failed_steps": [
                                    step.get("step_name")
                                    for step in pipeline_data.get("steps", [])
                                    if not step.get("success")
                                ],
                            }
                        )

        return {
            "user_query": question,
            "final_answer": agent_response,
            "conversation_id": conversation_id,
            "total_execution_time": total_execution_time,
            "thoughts": thoughts,
            "actions": actions,
            "pipelines_executed": list(set(pipelines_executed)),  # Remove duplicates
            "pipeline_step_times": pipeline_step_times,
            "failures": failures,
            "success": len(failures) == 0 and agent_response and agent_response != "",
        }

    def _create_detailed_result(
        self,
        question: str,
        agent_response: str,
        agent,
        conversation_id: str,
        tool_data: Dict[str, Any],
        pipeline_metrics: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Create detailed result with step-by-step pipeline information."""

        pipeline_details = []

        if pipeline_metrics and "pipelines" in pipeline_metrics:
            for pipeline_data in pipeline_metrics["pipelines"]:
                pipeline_name = pipeline_data.get("pipeline_name")

                # Get detailed step information with full input/output data
                detailed_steps = []
                for step in pipeline_data.get("steps", []):
                    step_detail = {
                        "step_name": step.get("step_name"),
                        "duration": step.get("duration"),
                        "success": step.get("success"),
                        "error_message": step.get("error_message"),
                        "input_data": step.get("input_data", {}),  # Full input data
                        "output_data": step.get("output_data", {}),  # Full output data
                        "input_data_keys": step.get("input_keys", []),
                        "output_data_keys": step.get("output_keys", []),
                        "timestamp": step.get("timestamp"),
                    }
                    detailed_steps.append(step_detail)

                pipeline_detail = {
                    "pipeline_name": pipeline_name,
                    "intent": pipeline_data.get("intent"),
                    "total_duration": pipeline_data.get("total_duration"),
                    "success": pipeline_data.get("success"),
                    "steps": detailed_steps,
                    "final_result": pipeline_data.get(
                        "final_result", {}
                    ),  # Full final result
                    "final_result_keys": pipeline_data.get("final_result_keys", []),
                }
                pipeline_details.append(pipeline_detail)

        # Add tool execution details
        tool_executions = tool_data.get("structured_data", {})

        return {
            "user_query": question,
            "final_answer": agent_response,
            "conversation_id": conversation_id,
            "pipeline_details": pipeline_details,
            "tool_executions": tool_executions,
            "raw_pipeline_metrics": pipeline_metrics,
        }

    def _create_error_summary_result(
        self, question: str, conversation_id: str, error: Exception, local_vars: Dict
    ) -> Dict[str, Any]:
        """Create summary result for failed executions."""
        return {
            "user_query": question,
            "final_answer": local_vars.get("agent_response", ""),
            "conversation_id": conversation_id,
            "total_execution_time": (
                time.time() - local_vars["start_time"]
                if "start_time" in local_vars
                else 0
            ),
            "thoughts": [],
            "actions": [],
            "pipelines_executed": [],
            "pipeline_step_times": {},
            "failures": [{"error": str(error)}],
            "success": False,
            "error": str(error),
        }

    def _create_error_detailed_result(
        self, question: str, conversation_id: str, error: Exception, local_vars: Dict
    ) -> Dict[str, Any]:
        """Create detailed result for failed executions."""
        return {
            "user_query": question,
            "final_answer": local_vars.get("agent_response", ""),
            "conversation_id": conversation_id,
            "pipeline_details": [],
            "tool_executions": {},
            "error": str(error),
            "raw_pipeline_metrics": {},
        }

    def save_results(
        self, results: List[Dict[str, Any]], filename: str = "agent_results.json"
    ) -> str:
        """
        Save the results to a JSON file.

        Args:
            results: List of result dictionaries
            filename: Name of the file to save

        Returns:
            Path to the saved file
        """
        # Ensure the filename has .json extension
        if not filename.endswith(".json"):
            filename += ".json"

        # Create the full path
        filepath = os.path.join(self.analytics_dir, filename)

        # Save the results
        with open(filepath, "w") as f:
            json.dump(results, f, indent=4)

        logger.info(f"Results saved to {filepath}")
        return filepath

    def load_results(
        self, filename: str = "agent_results.json"
    ) -> List[Dict[str, Any]]:
        """
        Load previously saved results.

        Args:
            filename: Name of the file to load

        Returns:
            List of result dictionaries
        """
        filepath = os.path.join(self.analytics_dir, filename)

        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Results file not found: {filepath}")

        with open(filepath, "r") as f:
            results = json.load(f)

        return results

    async def run_llm_judge(
        self,
        results_file: str = "agent_results.json",
        output_file: str = "llm_judge.json",
        force: bool = False,
    ) -> Dict[str, Any]:
        """
        Run the LLM Judge to evaluate agent responses sequentially.
        """
        # Check for existing results...

        # Load agent results
        results = self.load_results(results_file)
        evaluations = []

        logger.info(
            f"Running LLM Judge evaluations on {len(results)} results sequentially..."
        )

        # Evaluate each result one by one
        for i, result in enumerate(results):
            if not result.get("final_answer"):
                logger.info(f"Skipping result {i + 1}/{len(results)} - no final answer")
                continue

            logger.info(f"Evaluating result {i + 1}/{len(results)}")

            # Create a new session for each evaluation
            async with aiohttp.ClientSession() as session:
                # Initialize LLM Judge with session
                judge = LLMJudge(
                    project_id="impactai-430615",
                    location="us-east1",
                    model_name="gemini-2.0-flash-001",
                    session=session,
                )

                user_query = result.get("user_query", "")
                final_answer = result.get("final_answer", "")

                # Get the last LLM output with a prompt
                llm_outputs = result.get("llm_outputs", [])
                last_output = next(
                    (out for out in reversed(llm_outputs) if out.get("prompt")), {}
                )

                try:
                    # Evaluate this single result
                    evaluation = await judge.evaluate_response(
                        user_query, final_answer, last_output
                    )
                    evaluations.append(evaluation)
                except Exception as e:
                    logger.error(f"Error evaluating result {i + 1}: {e}")
                finally:
                    # Clean up the judge
                    await judge.cleanup()

            # Add a pause between evaluations
            await asyncio.sleep(2)

            # Save intermediate results
            judge_path = os.path.join(self.analytics_dir, output_file)
            judge_data = {
                "evaluations": [eval.dict() for eval in evaluations],
                "metrics": (
                    LLMJudge.compute_aggregate_metrics(evaluations)
                    if evaluations
                    else {}
                ),
                "timestamp": datetime.now().isoformat(),
            }
            with open(judge_path, "w") as f:
                json.dump(judge_data, f, indent=4)

        # Compute final aggregate metrics
        metrics = LLMJudge.compute_aggregate_metrics(evaluations) if evaluations else {}

        # Save final evaluations and metrics
        judge_data = {
            "evaluations": [eval.dict() for eval in evaluations],
            "metrics": metrics,
            "timestamp": datetime.now().isoformat(),
        }

        judge_path = os.path.join(self.analytics_dir, output_file)
        with open(judge_path, "w") as f:
            json.dump(judge_data, f, indent=4)

        logger.info(f"LLM Judge results saved to {judge_path}")

        # Update summary analytics if needed

        return metrics

    async def analyze_results(
        self,
        results_file: str = "agent_results.json",
        output_file: str = "summary_analytics.json",
        run_judge: bool = False,
    ) -> Dict[str, Any]:
        """
        Analyze the results and generate summary statistics.

        Args:
            results_file: Name of the results file to analyze
            output_file: Name of the file to save the analytics
            run_judge: Whether to run the LLM Judge

        Returns:
            Dictionary containing the analytics
        """
        # Load results
        results = self.load_results(results_file)

        # Initialize analytics dictionary
        analytics = {
            "total_questions": len(results),
            "timestamp": datetime.now().isoformat(),
            "failures": {},
            "iterations": {},
            "pipeline_sequences": {},
            "time_distribution": {},
            "pipelines": {},
        }

        # Count successful and failed queries
        successful_results = [r for r in results if r.get("success", False)]
        failed_results = [r for r in results if not r.get("success", False)]

        # 1. Percentage of failures
        analytics["failures"] = {
            "total": len(failed_results),
            "percentage": (len(failed_results) / len(results) * 100) if results else 0,
            "types": self._count_frequencies(
                [f.get("error", "Unknown") for f in failed_results]
            ),
        }

        # 2. Distribution of iteration numbers (based on thoughts)
        iterations = [len(r.get("thoughts", [])) for r in successful_results]

        analytics["iterations"] = {
            "min": min(iterations) if iterations else 0,
            "max": max(iterations) if iterations else 0,
            "mean": statistics.mean(iterations) if iterations else 0,
            "median": statistics.median(iterations) if iterations else 0,
            "distribution": self._count_frequencies(iterations),
        }

        # 3. Time distribution
        total_times = [r.get("total_execution_time", 0) for r in successful_results]
        action_times = [
            sum(action.get("execution_time", 0) for action in r.get("actions", []))
            for r in successful_results
        ]
        pipeline_times = [
            sum(
                pipeline_data.get("total_duration", 0)
                for pipeline_data in r.get("pipeline_step_times", {}).values()
            )
            for r in successful_results
        ]

        analytics["time_distribution"] = {
            "total_times": {
                "min": min(total_times) if total_times else 0,
                "max": max(total_times) if total_times else 0,
                "median": statistics.median(total_times) if total_times else 0,
                "mean": statistics.mean(total_times) if total_times else 0,
            },
            "action_times": {
                "min": min(action_times) if action_times else 0,
                "max": max(action_times) if action_times else 0,
                "median": statistics.median(action_times) if action_times else 0,
                "mean": statistics.mean(action_times) if action_times else 0,
            },
            "pipeline_times": {
                "min": min(pipeline_times) if pipeline_times else 0,
                "max": max(pipeline_times) if pipeline_times else 0,
                "median": statistics.median(pipeline_times) if pipeline_times else 0,
                "mean": statistics.mean(pipeline_times) if pipeline_times else 0,
            },
        }

        # 4. Pipeline distribution and performance
        analytics["pipelines"] = {}
        for result in successful_results:
            pipeline_step_times = result.get("pipeline_step_times", {})
            for pipeline_name, pipeline_data in pipeline_step_times.items():
                if pipeline_name not in analytics["pipelines"]:
                    analytics["pipelines"][pipeline_name] = {
                        "count": 0,
                        "success_count": 0,
                        "total_time": 0,
                        "steps": {},
                    }

                analytics["pipelines"][pipeline_name]["count"] += 1
                if pipeline_data.get("success", False):
                    analytics["pipelines"][pipeline_name]["success_count"] += 1
                analytics["pipelines"][pipeline_name][
                    "total_time"
                ] += pipeline_data.get("total_duration", 0)

                # Analyze steps
                for step in pipeline_data.get("steps", []):
                    step_name = step.get("step_name")
                    if step_name:
                        if (
                            step_name
                            not in analytics["pipelines"][pipeline_name]["steps"]
                        ):
                            analytics["pipelines"][pipeline_name]["steps"][
                                step_name
                            ] = {
                                "count": 0,
                                "success_count": 0,
                                "total_time": 0,
                            }

                        step_stats = analytics["pipelines"][pipeline_name]["steps"][
                            step_name
                        ]
                        step_stats["count"] += 1
                        if step.get("success", False):
                            step_stats["success_count"] += 1
                        step_stats["total_time"] += step.get("duration", 0)

        # Calculate averages for pipelines
        for pipeline_name, pipeline_stats in analytics["pipelines"].items():
            if pipeline_stats["count"] > 0:
                pipeline_stats["avg_time"] = (
                    pipeline_stats["total_time"] / pipeline_stats["count"]
                )
                pipeline_stats["success_rate"] = (
                    pipeline_stats["success_count"] / pipeline_stats["count"]
                ) * 100

                # Calculate averages for steps
                for step_name, step_stats in pipeline_stats["steps"].items():
                    if step_stats["count"] > 0:
                        step_stats["avg_time"] = (
                            step_stats["total_time"] / step_stats["count"]
                        )
                        step_stats["success_rate"] = (
                            step_stats["success_count"] / step_stats["count"]
                        ) * 100

        # 5. Most common pipeline sequences
        pipeline_sequences = [
            tuple(r.get("pipelines_executed", [])) for r in successful_results
        ]
        analytics["pipeline_sequences"] = {
            "most_common": self._get_most_common(pipeline_sequences),
            "total_unique": len(set(pipeline_sequences)),
        }

        # Save analytics
        analytics_path = os.path.join(self.analytics_dir, output_file)
        with open(analytics_path, "w") as f:
            json.dump(analytics, f, indent=4)

        # Run LLM Judge if requested
        if run_judge:
            judge_metrics = await self.run_llm_judge(results_file)
            analytics["llm_judge"] = judge_metrics

            # Save updated analytics
            with open(analytics_path, "w") as f:
                json.dump(analytics, f, indent=4)

        logger.info(f"Analytics saved to {analytics_path}")
        return analytics

    def count_failed_queries(self, results: List[Dict[str, Any]]) -> int:
        """Count the number of failed queries."""
        successful_results = []
        failed_queries = {"max_iterations": 0, "no_answer": 0}
        for result in results:
            final_answer = result.get("final_answer", "")
            if final_answer == "":
                failed_queries["no_answer"] += 1
            elif (
                final_answer == "Maximum iterations reached without finding an answer."
            ):
                failed_queries["max_iterations"] += 1
            else:
                successful_results.append(result)
        return failed_queries, successful_results

    def _count_error_types(
        self, failed_results: List[Dict[str, Any]]
    ) -> Dict[str, int]:
        """Count the frequency of different error types."""
        error_types = {}

        for result in failed_results:
            error_msg = result.get("error", "")

            # Extract the error type (everything before the colon in the error message)
            error_type = error_msg.split(":", 1)[0] if ":" in error_msg else error_msg
            error_type = error_type.strip()

            if error_type:
                error_types[error_type] = error_types.get(error_type, 0) + 1

        return error_types

    def _count_frequencies(self, items: List[Any]) -> Dict[str, int]:
        """Count the frequency of items in a list."""
        counter = Counter(items)
        return dict(counter)

    def _get_most_common(self, items: List[Any], n: int = 10) -> Dict[str, int]:
        """Get the n most common items in a list."""
        # Convert any non-string items to strings for use as dictionary keys
        string_items = [str(item) for item in items]
        counter = Counter(string_items)
        return dict(counter.most_common(n))

    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate the given percentile of a list of numbers."""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = (len(sorted_data) - 1) * percentile / 100
        floor_index = int(index)
        ceil_index = min(floor_index + 1, len(sorted_data) - 1)
        if floor_index == ceil_index:
            return sorted_data[floor_index]
        return sorted_data[floor_index] * (ceil_index - index) + sorted_data[
            ceil_index
        ] * (index - floor_index)


async def run_analysis_from_file(
    questions_file: str = "questions.json",
    output_filename: str = "agent_results.json",
    force: bool = False,
    run_analytics: bool = True,
    run_judge: bool = False,
) -> Dict[str, Any]:
    """
    Helper function to run analysis from a file and optionally generate analytics.
    """
    analytics = AgentAnalytics()
    questions = analytics.load_questions(questions_file)

    # Run agent on questions
    results = await analytics.run_agent_on_questions(
        questions, output_filename, force=force
    )

    # Run analytics if requested
    if run_analytics:
        analytics_results = await analytics.analyze_results(
            output_filename, run_judge=run_judge
        )
        return analytics_results

    return results


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Run agent analytics")
    parser.add_argument("--force", action="store_true", help="Force rerun of analysis")
    parser.add_argument(
        "--no-analytics", action="store_true", help="Skip analytics generation"
    )
    parser.add_argument(
        "--questions", type=str, default="questions.json", help="Questions file"
    )
    parser.add_argument(
        "--output", type=str, default="agent_results.json", help="Results output file"
    )
    parser.add_argument(
        "--llm-judge", action="store_true", help="Run LLM Judge evaluations"
    )

    args = parser.parse_args()

    async def main():
        try:
            # Just run the analysis directly - no batches
            await run_analysis_from_file(
                questions_file=args.questions,
                output_filename=args.output,
                force=args.force,
                run_analytics=not args.no_analytics,
                run_judge=args.llm_judge,
            )

        except Exception as e:
            logger.error(f"Error during analytics run: {e}")
        finally:
            # Close any sessions that might still be open from aiohttp
            for task in asyncio.all_tasks():
                if not task.done() and task != asyncio.current_task():
                    task.cancel()

            # Find and close any remaining client sessions
            import gc

            # Find and close any remaining client sessions
            for obj in gc.get_objects():
                if isinstance(obj, aiohttp.ClientSession) and not obj.closed:
                    try:
                        await obj.close()
                    except Exception as e:
                        logger.error(f"Error closing client session: {e}")

    asyncio.run(main())
