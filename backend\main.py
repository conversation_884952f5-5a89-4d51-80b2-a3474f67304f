import os
import logging
from contextlib import asynccontextmanager
import structlog
from structlog.processors import TimeStamper
import google.generativeai as genai

from fastapi import FastAP<PERSON>, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from middlewares.custom_middleware import handle_authentication_token

from middlewares.security_headers_middleware import SecurityHeadersMiddleware
from services.cache import (
    CacheService,
    destroy_redis_connection_pool,
    initialize_redis_connection_pool,
)

from routers import conversations, search, authentication, waitlists

DEBUG = os.getenv("DEBUG", "false").lower() == "true"

structlog.configure(
    processors=[
        TimeStamper(fmt="iso"),
        structlog.processors.JSONRenderer(),
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)


async def on_startup():
    gemini_api_key = os.getenv("GOOGLE_API_KEY")
    if gemini_api_key:
        genai.configure(api_key=gemini_api_key)
        logging.getLogger().info("Gemini API client configured.")
    else:
        logging.getLogger().warning(
            "GEMINI_API_KEY not found in environment variables. Gemini features will be disabled."
        )

    initialize_redis_connection_pool()
    logging.getLogger().info("started successfully")


async def on_shutdown():
    destroy_redis_connection_pool()
    logging.getLogger().info("shutdown successfully")


@asynccontextmanager
async def lifespan(_app: FastAPI):
    await on_startup()
    yield
    await on_shutdown()


app = FastAPI(debug=DEBUG, lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    max_age=86400,
)
app.add_middleware(SecurityHeadersMiddleware)

# app.middleware("http")(rate_limit_middleware)
app.middleware("http")(handle_authentication_token)
app.include_router(conversations.router)
app.include_router(search.router)
app.include_router(authentication.router)
app.include_router(waitlists.router)


@app.get("/")
def get_root():
    return {"success": True}


@app.delete("/cache")
def clear_cache():
    CacheService().clear()
    return {"success": True}


@app.get("/health")
async def health_check():
    try:
        return JSONResponse(
            status_code=status.HTTP_200_OK, content={"status": "healthy!"}
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={"status": "unhealthy", "error": str(e)},
        )
