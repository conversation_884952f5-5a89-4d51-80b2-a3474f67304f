import React from 'react';
import { Message, Source, Option, PlotResponse, PlotData } from "../types/ConversationTypes";

export const handleDownload = (url: string, fileName: string) => {
    const token = localStorage.getItem("token");

    fetch(url, {
        headers: {
            Authorization: token ? `Bearer ${token}` : "",
        },
    })
    .then((response) => {
        if (!response.ok) {
            throw new Error(`Failed to download file: ${response.statusText}`);
        }
        return response.blob();
    })
    .then((blob) => {
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = blobUrl;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(blobUrl);
    })
    .catch((error) => console.error("Download error:", error));
};

interface SectionData {
    information: Message[];
    answer: Message;
    plot: PlotResponse;
    sources: Source[];
    suggestedTopics: Option[];
    relatedTopics: Option[];
}

export const buildSections = (messages: Message[]) => {
    const sections = [];
    let currentSection: SectionData = { information: [], answer: null, plot: null, sources: null, suggestedTopics: null, relatedTopics: null };
    let pendingSuggestedTopics = null;
    let pendingRelatedTopics = null;
    let firstUserQuestionSeen = false;
    messages.forEach((msg, index) => {
        if (msg.type === 'answer' && msg.author === 'user') {
            firstUserQuestionSeen = true;
            if (currentSection.answer) {
                sections.push(currentSection);
            }
            currentSection = { information: [], answer: msg, plot: null, sources: null, suggestedTopics: pendingSuggestedTopics, relatedTopics: pendingRelatedTopics };
            pendingSuggestedTopics = null;
            pendingRelatedTopics = null;
        } else if (msg.type === 'information' && msg.author === 'system') {
            if (msg.plot?.data && msg.plot?.data.length > 0) {
                currentSection.plot = msg.plot;
            }
            if (msg.sources && msg.sources.length > 0) {
                currentSection.sources = msg.sources;
            }
            currentSection.information.push(msg);
        } else if (msg.type === 'question' && msg.author === 'system') {
            if (msg.choices) {
                if (msg.choices.type === 'suggested_topics') {
                    if (index === 0 || !firstUserQuestionSeen) {
                        currentSection.suggestedTopics = msg.choices.options;
                    } else if (currentSection.answer) {
                        currentSection.suggestedTopics = msg.choices.options;
                    } else {
                        pendingSuggestedTopics = msg.choices.options;
                    }
                } else if (msg.choices.type === 'related_topics') {
                    if (currentSection.answer) {
                        currentSection.relatedTopics = msg.choices.options;
                    } else {
                        pendingRelatedTopics = msg.choices.options;
                    }
                }
            }
        }
    });

    if (currentSection.answer || currentSection.information.length > 0 || currentSection.suggestedTopics || currentSection.relatedTopics) {
        sections.push(currentSection);
    }
    return sections;
};
type SummaryText = string | string[] | object;

export const formatSummaryText = (summaryText: SummaryText) => {
    if (typeof summaryText === "string") {
        return summaryText.trim();
    } else if (Array.isArray(summaryText)) {
        return summaryText.map(formatSummaryText).join(' ');
    } else if (React.isValidElement(summaryText)) {
        return formatSummaryText(summaryText.props.children);
    } else if (typeof summaryText === "object") {
        return JSON.stringify(summaryText, null, 2);
    }
    return "";
};

export const renderTextWithTags = (text: string, sources: Source[], plotData: PlotData[]) => {
    const updatedText = replaceTags(text, sources, plotData);
    return updatedText;
};

export const replaceTags = (text: string, sources: Source[], plotData: PlotData[]) => {
    const regexForPairs = /\[([^\]]+)\]/g;
    text = text.replace(regexForPairs, (_, group1) => {
        const [intervention, outcome] = group1.split(";").map((x) => x.trim());
        const interventionId = Number(intervention);
        const outcomeId = Number(outcome);
        const isValidPair = plotData.some(
            (plot) =>
                plot.intervention_id === interventionId &&
                plot.outcome_id === outcomeId &&
                plot.intervention_id !== undefined &&
                plot.outcome_id !== undefined
        );
        if (isValidPair) {
            const queryString = new URLSearchParams({
                intervention,
                outcome,
            }).toString();
            return `[](?${queryString})`;
        }
        return "";
    });
    const regexForAuthorYear = /\(?\(?\s*(\d{4})\s*\)?\s*:\s*\(?\s*(\d+)\s*\)?\)?/g;
    return text.replace(regexForAuthorYear, (match, year, pageNumber) => {
        year = year.trim();
        pageNumber = pageNumber.trim();
        const foundSource = sources.find((source) => source.paper_id === pageNumber);
        const paper_id = foundSource?.paper_id;
        const paperIndex = sources.findIndex(source => source.paper_id === paper_id);
        const paper_position = foundSource?.position || (paperIndex !== -1 ? paperIndex + 1 : 1);
        if (paper_id) {
            const queryString = new URLSearchParams({
                paper_id,
                paper_position: paper_position.toString(),
            }).toString();
            return `[](?${queryString})`;
        }
        return pageNumber;
    });
};

export const checkForIntroduction = (text: string) => {
    return /### Introduction/.test(text);
};

export const capitalizeFirstLetter = (text: string) => {
    return text ? text.charAt(0).toUpperCase() + text.slice(1) : text;
};

export const NormalizeSpaces = (text) =>
text.replace(/[^\S\r\n]+/g, ' ');
