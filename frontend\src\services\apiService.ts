import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

export const BASE_URL =
  window.location.hostname === 'localhost'
    ? 'https://api.impact-ai-dev.app'
    : `https://api.${window.location.hostname}`;

// Create an instance of Axios
const apiClient = axios.create({
  baseURL: BASE_URL,
});

// Add an interceptor to include the Authorization token in all requests
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Common function to make HTTP GET requests
export const get = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await apiClient.get<T>(url, config);
    return response.data;
  } catch (error) {
    console.error('GET request error:', error);
    throw error;
  }
};

// Common function to make HTTP POST requests
export const post = async <D, R>(url: string, data?: D, config?: AxiosRequestConfig): Promise<R> => {
  try {
    const response = await apiClient.post<D, AxiosResponse<R>>(url, data, config);
    return response.data;
  } catch (error) {
    console.error('POST request error:', error);
    throw error;
  }
};

// Common function to make HTTP PUT requests
export const put = async <T>(url: string, data?: T, config?: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await apiClient.put<T>(url, data, config);
    return response.data;
  } catch (error) {
    console.error('PUT request error:', error);
    throw error;
  }
};

// Common function to make HTTP DELETE requests
export const del = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await apiClient.delete<T>(url, config);
    return response.data;
  } catch (error) {
    console.error('DELETE request error:', error);
    throw error;
  }
};
