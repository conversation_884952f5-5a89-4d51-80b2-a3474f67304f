import React, { useState, useEffect, useRef } from "react";
import * as d3 from "d3";
import { useTheme } from "@mui/material/styles";
import {
  Box,
  Button,
  Collapse,
  ToggleButton,
  ToggleButtonGroup,
  Slider,
  Select,
  MenuItem,
} from "@mui/material";

const EffectSizesPlot2 = ({ data, xDomain }) => {
  const theme = useTheme();
  const containerRef = useRef<HTMLDivElement>(null);
  const [svgWidth, setSvgWidth] = useState(0);

  useEffect(() => {
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          setSvgWidth(entry.contentRect.width);
        }
      });
      resizeObserver.observe(containerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, []);

  const margin = {
    top: 20,
    right: 20,
    bottom: 0,
    left: 20,
  };

  const STUDY_HEIGHT = 16;
  const height = data[1].length * STUDY_HEIGHT + margin.top + margin.bottom;
  const width: number = containerRef.current?.offsetWidth || 0;
  const w: number = width - margin.left - margin.right;
  const h: number = height - margin.top - margin.bottom;

  const xScale = d3
    .scaleLinear()
    // .domain([
    //   Math.min(
    //     0,
    //     d3.min(data[1], (d) => d.standardized_ci_lower)
    //   ),
    //   Math.max(
    //     0,
    //     d3.max(data[1], (d) => d.standardized_ci_upper)
    //   ),
    // ])
    .domain(xDomain)
    .range([0, w])
    .nice();

  const cScale = d3
    .scaleLinear()
    .domain([-0.8, -0.5, -0.2, 0, 0.2, 0.5, 0.8])
    .range([
      "#8aaf5f",
      "#afc183",
      "#c7d6ae",
      "#ffffff",
      "#a7c6e1",
      "#7aabd2",
      "#4e8fc0",
    ])
    .clamp(true);

  return (
    <Box
      className="funnel-plot-box"
      ref={containerRef}
      style={{ background: "rgba(245, 249, 254, 1)", paddingTop: 10, paddingBottom: 4, marginBottom: 2}}
    >
      <div>
        <h5 style={{ marginInline: 20, marginBlock: 0}}>{data[0]}</h5>
        <svg width={svgWidth} height={height + margin.bottom + margin.top}>
          <defs>
            <linearGradient
              id={`gradient-${xScale.domain()[0]}-${xScale.domain()[1]}`}
              x1="0%"
              y1="0%"
              x2="100%"
              y2="0%"
            >
              {xScale.ticks(5).map((stop) => {
                return (
                  <stop
                    offset={`${
                      ((stop - xScale.domain()[0]) /
                        (xScale.domain()[1] - xScale.domain()[0])) *
                      100
                    }%`}
                    stop-color={`${cScale(stop)}`}
                  />
                );
              })}
            </linearGradient>
          </defs>
          <g transform={`translate(${margin.left}, ${margin.top})`}>
            <line
              x1={xScale.range()[0]}
              y1={h}
              x2={xScale.range()[1]}
              y2={h}
              stroke="rgb(22 54 97)"
              opacity={0.3}
            />
            <line
              x1={xScale(0)}
              y1={h}
              x2={xScale(0)}
              y2={-margin.top}
              // stroke="#ccc"
              stroke="black"
              strokeDasharray="2 2"
            />

            <text
              x={w / 2}
              y={h}
              dy={30}
              style={{
                fontSize: 12,
                textAnchor: "middle",
                fontWeight: "bold",
                fill: "rgb(22 54 97)",
              }}
            >
              effect size
            </text>
            {/* {meanEffectValue !== undefined && (
              <g
                className="mean-effect"
                transform={`translate(${xScale(meanEffectValue)}, 0)`}
              >
                <text dy={-20}>mean</text>
                <line y1={-18} y2={h} />
              </g>
            )} */}
            {data[1].sort((a, b) => b.cohen_d - a.cohen_d).map((study, i) => (
              <g
                transform={`translate(0, ${i * STUDY_HEIGHT})`}
                className="study"
                // opacity={
                //   hoveredIntervention === undefined ||
                //   hoveredIntervention === study.intervention_ids
                //     ? 1
                //     : 0.1
                // }
                // onPointerOver={() => onStudyPointerOver(study)}
                // onPointerOut={() => onStudyPointerOut()}
              >
                <rect
                  width={w}
                  height={STUDY_HEIGHT}
                  y={-STUDY_HEIGHT / 2}
                  // fill="rgb(245 249 254)"
                  fill="transparent"
                  //   opacity={
                  //     (selectedStudy !== selectedStudy?.paper_id) ===
                  //     study.paper_id
                  //       ? 1
                  //       : 0
                  //   }
                />
                <line
                  x1={xScale(study.standardized_ci_lower)}
                  x2={xScale(study.standardized_ci_upper)}
                  stroke="rgb(22 54 97)"
                />
                <line
                  x1={xScale(study.standardized_ci_lower)}
                  y1={-4}
                  x2={xScale(study.standardized_ci_lower)}
                  y2={4}
                  stroke="rgb(22 54 97)"
                />
                <line
                  x1={xScale(study.standardized_ci_upper)}
                  y1={-4}
                  x2={xScale(study.standardized_ci_upper)}
                  y2={4}
                  stroke="rgb(22 54 97)"
                />
                <g>
                  <circle
                    className="outline"
                    cx={xScale(study.cohen_d)}
                    r={6}
                    fill={`${cScale(study.cohen_d)}`}
                    fillOpacity={0.7}
                    stroke="rgb(22 54 97)"
                  />
                  <circle
                    cx={xScale(study.cohen_d)}
                    r={2}
                    fill="rgb(22 54 97)"
                    opacity={0.8}
                  />
                </g>
              </g>
            ))}
            {xScale.ticks(5).map((tick) => (
              <text
                y={h}
                x={xScale(tick)}
                dominantBaseline="central"
                dy={10}
                textAnchor="middle"
                fontSize={10}
                fill="rgb(22 54 97)"
              >
                {tick}
              </text>
            ))}
          </g>
        </svg>
      </div>
    </Box>
  );
};

export default EffectSizesPlot2;
