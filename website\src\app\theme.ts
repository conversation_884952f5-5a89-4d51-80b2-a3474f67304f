import { createTheme } from '@mui/material/styles';
import { Roboto } from 'next/font/google';

const roboto = Roboto({
    subsets: ['latin'],
    weight: ['400', '500'],
});
const primaryHeaderColor = '#002244';
const primaryBodyColor = '#004370';

const theme = createTheme({
    typography: {
        fontFamily: [
            'Open Sans',
            roboto.style.fontFamily,
            'Helvetica',
            'Arial',
            'sans-serif',
        ].join(','),
        h1: {
            fontFamily: 'HostGrotesk, Open Sans, Roboto, Helvetica',
            fontWeight: '500',
            [createTheme().breakpoints.down('sm')]: {
                fontSize: '28px',
                lineHeight: '40px',
                fontWeight: '600',
            }, // Mobile size
            [createTheme().breakpoints.up('lg')]: {
                fontSize: '40px',
                lineHeight: '52px',
            },  // Mobile size
            letterSpacing: 0,
            color: `${primaryHeaderColor} !important`
        },
        h2: {
            fontFamily: 'HostGrotesk, Open Sans, Roboto, Helvetica',
            fontWeight: '500',
            [createTheme().breakpoints.down('sm')]: { fontSize: '28px' }, // Mobile size
            [createTheme().breakpoints.up('lg')]: { fontSize: '36px' },  // Desktop size
            lineHeight: '43px',
            letterSpacing: 0,
            color: `${primaryHeaderColor} !important`
        },
        h3: {
            fontFamily: 'HostGrotesk, Open Sans, Roboto, Helvetica',
            fontWeight: '600',
            [createTheme().breakpoints.down('sm')]: { fontSize: '26px' }, // Mobile size
            [createTheme().breakpoints.up('lg')]: { fontSize: '32px' },  // Desktop size
            lineHeight: '30px',
            letterSpacing: 0,
            color: `${primaryHeaderColor} !important`
        },
        h4: {
            fontFamily: 'HostGrotesk, Open Sans, Roboto, Helvetica',
            fontWeight: '600',
            [createTheme().breakpoints.down('sm')]: { fontSize: '20px' }, // Mobile size
            [createTheme().breakpoints.up('lg')]: { fontSize: '28px' },  // Desktop size
            lineHeight: '30px',
            letterSpacing: 0,
            color: `${primaryHeaderColor} !important`
        },
        h5: {
            fontFamily: 'HostGrotesk, Open Sans, Roboto, Helvetica',
            fontWeight: '600',
            [createTheme().breakpoints.down('sm')]: { fontSize: '16px' }, // Mobile size
            [createTheme().breakpoints.up('lg')]: { fontSize: '24px' },  // Desktop size
            lineHeight: '26px',
            letterSpacing: 0,
            color: `${primaryHeaderColor} !important`
        },
        h6: {
            fontFamily: 'HostGrotesk, Open Sans, Roboto, Helvetica',
            fontWeight: '600',
            fontSize: '16px',
            lineHeight: '26px',
            letterSpacing: 0,
            color: `${primaryHeaderColor} !important`
        },
        button: {
            fontFamily: 'HostGrotesk,  Open Sans, Roboto, Helvetica',
            fontSize: '15px',
            fontWeight: '500',
        },
        body1: {
            fontFamily: 'Open Sans, Roboto',
            fontWeight: '400',
            lineHeight: '26px',
            [createTheme().breakpoints.down('sm')]: { fontSize: '16px' }, // Mobile size
            [createTheme().breakpoints.up('md')]: { fontSize: '16px' }, // Tablet size
            [createTheme().breakpoints.up('lg')]: { fontSize: '16px' },  // Desktop size
            color: `${primaryBodyColor} !important`
        },
        body2: {
            fontFamily: 'Open Sans, Roboto',
            fontWeight: '400',
            lineHeight: '20px',
            [createTheme().breakpoints.down('sm')]: { fontSize: '14px' }, // Mobile size
            [createTheme().breakpoints.up('md')]: { fontSize: '14px' }, // Tablet size
            [createTheme().breakpoints.up('lg')]: { fontSize: '14px' },  // Desktop size
            color: `${primaryBodyColor} !important`
        },
        caption: {
            fontFamily: 'Open Sans, Roboto',
            color: `${primaryBodyColor} !important`
        }
    },
    palette: {
        primary: {
            main: 'rgba(0, 67, 112, 1)', // #004370 100% new RGB
            dark: 'rgba(0, 51, 128, 1)', // #003380 100%
            light: 'rgba(71, 143, 252, 1)', // #478FFC 100%
        },
        secondary: {
            main: 'rgba(131, 179, 252, 1)', // #83B3FC 100%
            dark: 'rgba(0, 92, 229, 1)', // #005CE5 100%
            light: 'rgba(212, 228, 252, 1)', // #D4E4FC 100%
        },
        error: {
            main: 'rgba(233, 59, 51, 1)', // E93B33 100%
            dark: 'rgba(175, 44, 38, 1)', // AF2C26 100%
            light: 'rgba(244, 157, 153, 1)', // F49D99 100%
        },
        warning: {
            main: 'rgba(241, 157, 56, 1)', // F19D38 100%
            dark: 'rgba(181, 118, 42, 1)', // B5762A 100%
            light: 'rgba(248, 206, 155, 1)', // F8CE9B 100%
        },
        info: {
            main: 'rgba(0, 176, 255, 1)', // 00B0FF 100%
            dark: 'rgba(26, 135, 204, 1)', // 1A87CC 100%
            light: 'rgba(196, 236, 254, 1)', // C4ECFE 100%
        },
        success: {
            main: 'rgba(102, 189, 80, 1)', // 66BD50 100%
            dark: 'rgba(51, 95, 40, 1)', // 335F28 100%
            light: 'rgba(178, 222, 167, 1)', // B2DEA7 100%
        },
        mode: 'light',
        common: {
            black: 'rgba(36, 40, 42, 1)', // #24282A 100%
            white: 'rgba(255, 255, 255, 1)', // #FFFFFF 100%
        },
        text: {
            primary: 'rgba(7, 60, 116, 1)', // '#073C74' 100%
            secondary: 'rgba(0, 51, 128, 0.7)', // '163763' 70% #003380B2
            disabled: 'rgba(22, 54, 97, 0.38)',
        },
        divider: 'rgba(171, 204, 252, 0.5)', // #ABCCFC at 50% opacity
        action: {
            active: 'rgba(0, 67, 112, 1)', // #004370 100% new RGB
            hover: 'rgba(0, 71, 178, 0.65)', // 0047B2 65% 
            selected: 'rgba(0, 71, 178, 0.09)', // 0047B217 9%
            disabled: 'rgba(0, 51, 128, 0.38)', // 003380 38%
            disabledBackground: 'rgba(212, 228, 252, 1)', // D4E4FC 100%
            focus: 'rgba(0, 71, 178, 1)', // 0047B2 100% 
        },
        background: {
            default: 'rgba(255, 255, 255, 1)', // FFFFFF 100%
            paper: 'rgba(245, 249, 254, 1)', // F5F9FE 100%
        },
    },
});

export default theme;