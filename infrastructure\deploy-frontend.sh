#!/bin/bash

set -e

source ./infrastructure/check-gcloud.sh
source ./infrastructure/check-env.sh

export FULL_SERVICE_NAME="impactai-$SERVICE-$TARGET"

PROJECT_ID="impactai-430615"
REGION="us-east1"

TIMESTAMP=$(date +%Y%m%d%H%M%S)
export IMAGE_NAME="gcr.io/$PROJECT_ID/$SERVICE:$TIMESTAMP"

echo "Building and pushing Docker image..."
gcloud builds submit $SERVICE --tag $IMAGE_NAME

echo "Deploying $FULL_SERVICE_NAME"
gcloud run deploy $FULL_SERVICE_NAME \
  --image $IMAGE_NAME \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --memory 2048Mi \
  --cpu 2 \
  --port 3000 \
  --set-env-vars "TARGET=$TARGET" \
  --set-env-vars "NODE_ENV=production" \

echo "$FULL_SERVICE_NAME deployment successful"
