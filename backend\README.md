# Backend Service

The backend service is built with FastAPI and provides the core API functionality for the ImpactAI platform.

## Architecture

### Main Components

- **FastAPI Application**: Core API server with middleware and routers
- **Database Models**: SQLAlchemy models for data persistence
- **Authentication**: JWT-based authentication system
- **WebSocket Support**: Real-time communication for chat features
- **Redis Cache**: Performance optimization through caching
- **OpenTelemetry**: Integrated monitoring and tracing

### API Routes
Most uptodate list of endpoints can be found after you run the app `/docs` endpoint.

1. **Authentication**
   - `/auth/login` - User login
   - `/auth/register` - User registration
   - `/auth/forgot-password` - Password recovery
   - `/auth/change-password` - Password change

2. **Conversations**
   - `/conversations` - Manage chat conversations
   - WebSocket endpoint for real-time messaging

3. **Search**
   - `/search/chips` - Search interface components
   - `/search/tags` - Tag-based search functionality

4. **Waitlist**
   - `/waitlist` - Manage user waitlist

### Middleware

- CORS Support
- Security Headers
- Authentication Token Handling
- Rate Limiting (configurable)

## Setup

### Requirements

```txt
fastapi==0.110.0
uvicorn[standard]==0.27.1
redis==5.0.2
sqlalchemy==2.0.28
pymysql==1.1.1
databases==0.9.0
aiomysql==0.2.0
structlog==24.4.0
```

### Environment Variables

Required environment variables:
- Database configuration
- Redis settings
- JWT secret
- API keys for external services

## Features

### Security
- JWT authentication
- Password hashing with bcrypt
- Security headers middleware
- Rate limiting capability

### Data Management
- SQL database integration
- Redis caching
- Structured logging
- Error handling

### Real-time Features
- WebSocket support for live chat
- Conversation management
- Message feedback system

### Monitoring
- OpenTelemetry integration
- Health check endpoint
- Structured logging
- Performance metrics

## API Documentation

The API documentation is available at `/docs` when running the server, providing interactive documentation for all endpoints.
