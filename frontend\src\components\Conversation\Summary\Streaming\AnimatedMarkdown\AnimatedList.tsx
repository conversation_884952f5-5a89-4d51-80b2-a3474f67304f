import React, { useState, useRef, useEffect, useCallback } from 'react';
import AnimatedParagraph from "./AnimatedParagraph";

interface AnimatedListProps {
  children: React.ReactNode;
  onComplete: () => void;
  start?: number;
}

const AnimatedList = React.memo(({ children, onComplete }: AnimatedListProps) => {
  const [currentItemIndex, setCurrentItemIndex] = useState(0);
  const childrenArray = React.Children.toArray(children);

  const handleItemAnimated = useCallback((index: number) => {
    if (index === currentItemIndex) {
      if (index === childrenArray.length - 1) {
        onComplete();
      } else {
        setCurrentItemIndex(index + 1);
      }
    }
  }, [childrenArray.length, currentItemIndex, onComplete]);

  return (
    <>
      {childrenArray.map((child, index) => {
        const content = child.type === "li" ? child.props.children : child;
        return (
          <li
            key={index}
            style={{
              opacity: index <= currentItemIndex ? 1 : 0,
              display: 'list-item',
            }}
          >
            {index <= currentItemIndex && (
              <AnimatedParagraph
                onComplete={() => handleItemAnimated(index)}
              >
                {content}
              </AnimatedParagraph>
            )}
          </li>
        );
      })}
    </>
  );
});

export default AnimatedList;