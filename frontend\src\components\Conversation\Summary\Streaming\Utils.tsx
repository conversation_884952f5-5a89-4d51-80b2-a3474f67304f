import { Source, PlotData } from "../../../../types/ConversationTypes";

type SummaryText = string | string[] | object;

export const formatSummaryText = (summaryText: SummaryText) => {
    if (typeof summaryText === "string") {
        return summaryText;
    } else if (Array.isArray(summaryText)) {
        return summaryText.join(",");
    } else if (typeof summaryText === "object") {
        return JSON.stringify(summaryText, null, 2);
    }
    return "";
};

export const renderTextWithTags = (text: string, sources: Source[], plotData: PlotData[], combinePaperLinks: boolean = true) => {
    const updatedText = replaceTags(text, sources, plotData, combinePaperLinks);
    return updatedText;
};

export const replaceTags = (text: string, sources: Source[], plotData: PlotData[], combinePaperLinks: boolean = true) => {
    const regexForTextWithParentheticalId = /\[([^\]]+?)\]\((\d+)\)/g;
    text = text.replace(regexForTextWithParentheticalId, (match, p1, p2) => {
        return `[${p1}]`;
    });

    const regexForStandaloneShortPaperId = /\[([A-Za-z]\d+(?:\s*,\s*[A-Za-z]\d+)*)\]/g;
    text = text.replace(regexForStandaloneShortPaperId, (_, shortPaperIdGroup) => {
        const shortPaperIds = shortPaperIdGroup.split(',').map((x: string) => x.trim());

        if (combinePaperLinks) {
            const combinedParams = new URLSearchParams();
            const foundPaperIds: string[] = [];

            shortPaperIds.forEach((shortPaper: string) => {
                const foundSource = sources.find(source => source.short_paper_id === shortPaper);
                if (foundSource && foundSource.paper_id) {
                    foundPaperIds.push(foundSource.paper_id);
                }
            });

            foundPaperIds.forEach(id => combinedParams.append('paper_id', id));

            if (foundPaperIds.length > 0) {
                return `[](?${combinedParams.toString()})`;
            } else {
                return '';
            }
        } else {
            let replacement = '';
            shortPaperIds.forEach((shortPaper: string) => {
                const foundSource = sources.find(source => source.short_paper_id === shortPaper);
                if (foundSource) {
                    const queryString = new URLSearchParams({
                        paper_id: foundSource.paper_id || '',
                        paper_uniqueID: foundSource.id || '',
                        paper_doi_url: foundSource.doi_url || '',
                        paper_position: (foundSource.position !== undefined ? foundSource.position.toString() : '1'),
                        paper_title: foundSource.title || '',
                        paper_citation: foundSource.citation || '',
                    }).toString();
                    replacement += `[](?${queryString})`;
                }
            });
            return replacement;
        }
    });

    const regexForEffectSizeCitationId = /\[(#\d+(?:,\s*#\d+)*)\]/g;
    text = text.replace(regexForEffectSizeCitationId, (_, citationIdGroup) => {
        const rawCitationIds = citationIdGroup.split(',').map((x: string) => x.trim());
        const citationParams = new URLSearchParams();
        const foundCitationIds: string[] = [];

        rawCitationIds.forEach((rawId: string) => {
            const numericId = rawId.replace(/^#/, '');
            if (numericId) {
                foundCitationIds.push(numericId);
            }
        });

        foundCitationIds.forEach(id => citationParams.append('citation_id', id));

        if (foundCitationIds.length > 0) {
            return `[](?${citationParams.toString()})`;
        } else {
            return '';
        }
    });

    return text;
};

export const checkForIntroduction = (text: string) => {
    return /### Introduction/.test(text);
};

export const capitalizeFirstLetter = (text: string) => {
    return text ? text.charAt(0).toUpperCase() + text.slice(1) : text;
};