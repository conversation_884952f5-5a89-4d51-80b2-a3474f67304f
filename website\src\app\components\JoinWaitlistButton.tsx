"use client";
import React from "react";
import { Box, Button } from "@mui/material";
import { useRouter } from "next/navigation";
import { useTheme } from "@mui/material/styles";

const JoinWaitlistButton = () => {
  const router = useRouter();
  const theme = useTheme();

  const handleJoinWaitlist = () => {
    router.push("/jointhewaitlist");
  };

  return (
    <Box>
      <Button
        variant="contained"
        color="primary"
        onClick={handleJoinWaitlist}
        sx={{
          borderRadius: "64px",
          background: theme.palette.action.active,
          boxShadow: "0px 1px 5px 0px rgba(0, 0, 0, 0.05), 0px 2px 2px 0px rgba(0, 0, 0, 0.05), 0px 3px 1px -2px rgba(0, 0, 0, 0.05)",
          color: theme.palette.background.default,
          fontFamily: "HostGrotesk",
          fontStyle: "normal",
          fontWeight: "600",
          fontSize: "14px",
          textTransform: "uppercase",
          padding: "6px 16px",
          display: "flex",
          lineHeight: "24px",
          letterSpacing: "1px",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        JOIN THE WAITLIST
      </Button>
    </Box>
  );
};

export default JoinWaitlistButton;