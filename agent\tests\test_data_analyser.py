import requests

url = "https://data-analysis-din4qs2qka-uc.a.run.app/analyze"  # Replace with actual host/port
payload = {
    "query": "What are the effects of cash transfers on education outcomes?",
    "entities": {
        "interventions": [
            {"id": 123, "label": "Cash Transfer Programs", "mention": "cash transfers"}
        ],
        "outcomes": [
            {"id": 456, "label": "Education Outcomes", "mention": "education outcomes"}
        ],
    },
    # You can also include "dataset": ... if needed
}

response = requests.post(url, json=payload)

if response.status_code == 200:
    print("Generated prompt:", response.json().keys())  # Adjust key based on response
else:
    print("Request failed:", response.status_code, response.text)
