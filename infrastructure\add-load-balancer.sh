#!/bin/bash

PROJECT_ID="impactai-430615"
SERVICE_NAME="impactai-custom-site-development"
REGION="us-east1"
NEG_NAME="impactai-custom-site-neg"
BACKEND_NAME="impactai-custom-site-backend"
LB_NAME="impactai-custom-site-lb"
IP_NAME="impactai-custom-site-lb-ip"
PROXY_NAME="impactai-custom-site-proxy"
RULE_NAME="impactai-custom-site-rule"
CERT_NAME="impactai-custom-site-cert"
POLICY_NAME="impactai-custom-site-policy"
DOMAIN="impact-ai-dev.app"

set -e
# Helper function to check if resource exists
resource_exists() {
  gcloud compute $1 describe $2 --global --format="value(name)" > /dev/null 2>&1
  return $?
}

echo "Checking/creating Serverless NEG..."
if ! gcloud compute network-endpoint-groups describe $NEG_NAME --region=$REGION > /dev/null 2>&1; then
  gcloud compute network-endpoint-groups create $NEG_NAME \
    --region=$REGION \
    --network-endpoint-type=serverless \
    --cloud-run-service=$SERVICE_NAME
fi

echo "Checking/reserving static IP..."
if ! resource_exists "addresses" $IP_NAME; then
  gcloud compute addresses create $IP_NAME \
    --network-tier=PREMIUM \
    --ip-version=IPV4 \
    --global
fi

echo "Checking/creating backend service..."
if ! resource_exists "backend-services" $BACKEND_NAME; then
  gcloud compute backend-services create $BACKEND_NAME \
    --global \
    --load-balancing-scheme=EXTERNAL \
    --protocol=HTTPS
fi

echo "Checking/adding NEG to backend service..."
if ! gcloud compute backend-services describe $BACKEND_NAME --global --format="value(backends)" | grep -q $NEG_NAME; then
  gcloud compute backend-services add-backend $BACKEND_NAME \
    --global \
    --network-endpoint-group=$NEG_NAME \
    --network-endpoint-group-region=$REGION || echo "NEG already attached or error occurred"
fi

echo "Checking/creating URL map..."
if ! resource_exists "url-maps" $LB_NAME; then
  gcloud compute url-maps create $LB_NAME \
    --default-service=$BACKEND_NAME \
    --global
fi

echo "Checking/creating SSL certificate..."
if ! resource_exists "ssl-certificates" $CERT_NAME; then
  gcloud compute ssl-certificates create $CERT_NAME \
    --domains=$DOMAIN \
    --global
fi

echo "Checking/creating target HTTPS proxy..."
if ! resource_exists "target-https-proxies" $PROXY_NAME; then
  gcloud compute target-https-proxies create $PROXY_NAME \
    --url-map=$LB_NAME \
    --ssl-certificates=$CERT_NAME
fi

echo "Checking/creating forwarding rule..."
if ! resource_exists "forwarding-rules" $RULE_NAME; then
  gcloud compute forwarding-rules create $RULE_NAME \
    --global \
    --load-balancing-scheme=EXTERNAL \
    --address=$IP_NAME \
    --ports=443 \
    --target-https-proxy=$PROXY_NAME
fi

echo "Checking/creating Cloud Armor policy..."
if ! gcloud compute security-policies describe $POLICY_NAME --global > /dev/null 2>&1; then
  gcloud compute security-policies create $POLICY_NAME \
    --description "Security policy for ImpactAI custom site"
fi

echo "Checking/adding Cloud Armor rule..."
if ! gcloud compute security-policies rules describe 1000 --security-policy=$POLICY_NAME > /dev/null 2>&1; then
  gcloud compute security-policies rules create 1000 \
    --security-policy=$POLICY_NAME \
    --description "Deny specific IP range" \
    --src-ip-ranges="***********/24" \
    --action=deny-403
fi

echo "Attaching Cloud Armor policy to backend..."
gcloud compute backend-services update $BACKEND_NAME \
  --security-policy=$POLICY_NAME \
  --global

echo "Deployment complete!"
echo "Load balancer IP: $(gcloud compute addresses describe $IP_NAME --global --format='get(address)')"
echo "Update your DNS to point $DOMAIN to this IP."