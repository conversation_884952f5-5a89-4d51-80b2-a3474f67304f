import React from "react";
import { Paper, Box, Grid, Typography, Card, CardContent, CardMedia } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";

const Section3 = () => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const isMobileOrTablet = isMobile || isTablet;

    const features = [
        {
            image: "/images/home/<USER>",
            title: "Product interface displaying answer summary text",
            description: "High-quality research evidence with clear quantitative insights"
        },
        {
            image: "/images/home/<USER>",
            title: "Product interface displaying sources panel",
            description: "Directly sourced information with accurate attributions"
        },
        {
            image: "/images/home/<USER>",
            title: "Product interface displaying graph output",
            description: "Interactive visualizations bringing evidence to life"
        },
    ];

    return (
        <Paper
            sx={{
                padding: "0px",
                width: "100%",
                height: "auto",
                background: "none",
                boxShadow: 0,
                borderRadius: "8px",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: isMobile ? "16px" : isTablet ? "40px" : "77px",
            }}
        >
            {/* Header Section */}
            <Box sx={{ textAlign: "center", width: isMobile ? "100%" : isTablet ? "100%" : "804px" }}>
                <Typography variant={isMobile ? "body2" : isTablet ? "h3" : "h2"} sx={{ fontWeight: '500' }}>
                    ImpactAI translates vast research into actionable insights, empowering policy and development practitioners with:
                </Typography>
            </Box>

            {/* Main Image Section */}
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    width: "100%",
                    mt: 2,
                }}
            >
                <Grid container spacing={isMobile ? 0 : 3} justifyContent="center" m={0} p={0}>
                    {features.map((feature, index) => (
                        <Grid item xs={12} sm={4} md={4} key={index}
                            sx={{
                                p: '0px !important',
                                mb: isMobile ? (index !== features.length - 1 ? 2 : 0) : 0
                            }}>
                            <Card
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "center",
                                    borderRadius: "16px",
                                    px: 2,
                                    boxShadow: 0,
                                    height: 'auto',
                                    background: 'none',
                                    gap: isMobile ? "8px" : isTablet ? "11.6px" : "21px"
                                }}
                            >
                                <CardMedia
                                    component="img"
                                    image={feature.image}
                                    alt={feature.title}
                                    sx={{ width: "100%", height: isMobileOrTablet ? "auto" : "282px", borderRadius: "12px", objectFit: "contain" }}
                                />
                                <CardContent sx={{ textAlign: "center", width: "90%", p: 0, m: 0, pb: '8px !important' }}>
                                    <Typography variant={isMobileOrTablet ? "body2" : "h4"} sx={{ color: `${theme.palette.primary.main} !important`, fontWeight: isMobileOrTablet ? "400" : "600" }}>
                                        {feature.description}
                                    </Typography>
                                </CardContent>
                            </Card>
                        </Grid>
                    ))}
                </Grid>
            </Box>
        </Paper>
    );
};

export default Section3;
