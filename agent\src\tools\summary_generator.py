"""Summary generation module for query results."""

from typing import Dict, Any
from pydantic import BaseModel
import logging
from src.tools.base import Tool
import aiohttp
import json


logger = logging.getLogger(__name__)

FINAL_ANSWER_API_URL = (
    "https://final-answer-564807556547.us-central1.run.app/final_answer"
)


class FinalAnswer(BaseModel):
    """Generated final answer with metadata."""

    text: str


class FinalAnswerGenerator(Tool):
    """Tool for generating final answers from Structured Data results."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the final answer generator."""
        super().__init__(
            name="final_answer_generator",
            description="Generate a concise, well-formed answer summarizing research findings and data analysis results based on the provided data and research context",
            func=self.generate,
            arguments=[
                ("user_query", "str"),
                (
                    "structured_data",
                    "StructuredData(text: str, conversation_history: None, error_message: None, url: None)",
                ),
            ],
            outputs=[
                (
                    "final_answer",
                    "FinalAnswer(text: str)",
                ),
            ],
            config=config,
        )
        self.verbose = config.get("verbose", False)

    async def generate(
        self,
        user_query: str,
        structured_data: Dict[str, Any] | None = None,
    ) -> FinalAnswer:
        """Generate a final answer from Structured Data Analysis results."""
        try:
            # Extract text from structured_data dictionary
            text = None
            if structured_data is not None:
                if isinstance(structured_data, dict):
                    text = structured_data.get("text", "")
                else:
                    text = str(structured_data)

            # Prepare request payload for summarizer API
            payload = {"query": user_query}
            if text:
                payload["reference_analysis"] = text

            # Log the payload
            if self.verbose:
                simplified_payload = {
                    "query": user_query,
                    "reference_analysis": text[:200] + "..." if text else "None",
                }
                logger.info(
                    f"Sending payload to Final Answer API: {json.dumps(simplified_payload, indent=2)}"
                )

            # Make API request to summarizer
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    FINAL_ANSWER_API_URL,
                    json=payload,
                    headers={
                        "accept": "application/json",
                        "Content-Type": "application/json",
                    },
                ) as response:
                    response_text = await response.text()

                    if response.status != 200:
                        raise Exception(
                            f"Final Answer API returned status {response.status}: {response_text}"
                        )

                    try:
                        result = json.loads(response_text)
                    except json.JSONDecodeError as e:
                        raise Exception(
                            f"Failed to parse API response: {e}\nResponse text: {response_text}"
                        )

                    # Log the response
                    if self.verbose:
                        simplified_response = {
                            "answer": result.get("answer", "N/A")[:200] + "...",
                            "metadata": {
                                "prompt": result.get("metadata", {}).get(
                                    "prompt", "N/A"
                                )[:200]
                                + "...",
                            },
                        }
                        logger.info(
                            f"Final Answer API Response: {json.dumps(simplified_response, indent=2)}"
                        )

                    # Create summary with metadata
                    final_answer = FinalAnswer(
                        text=result["answer"].strip(),
                    )

                    return final_answer

        except Exception as e:
            logger.error(f"Error generating final answer: {str(e)}")
            raise
