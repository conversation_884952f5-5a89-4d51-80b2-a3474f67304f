"""Summary generation module for query results."""

from typing import Dict, Any, List
from pydantic import BaseModel
import logging
from src.tools.base import Tool
import aiohttp
import json

from src.tools.rag_searcher import RAGResults
from src.tools.structure_data_organizer import StructuredData

logger = logging.getLogger(__name__)

FINAL_ANSWER_API_URL = (
    "https://final-answer-564807556547.us-central1.run.app/final_answer"
    # "http://localhost:8000/final_answer"  # For local testing, change as needed
)


class FinalAnswer(BaseModel):
    """Generated final answer with metadata."""

    text: str


class FinalAnswerGenerator(Tool):
    """Tool for generating final answers from Structured Data results."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the final answer generator."""
        super().__init__(
            name="final_answer_generator",
            description="Generate a concise, well-formed answer summarizing research findings and data analysis results based on the provided data and research context",
            func=self.generate,
            arguments=[
                ("user_query", "str"),
                (
                    "structured_data",
                    "StructuredData(text: str, conversation_history: None, error_message: None, url: None)",
                ),
            ],
            outputs=[
                (
                    "final_answer",
                    "FinalAnswer(text: str)",
                ),
            ],
            config=config,
        )
        self.verbose = config.get("verbose", False)

    async def generate(
        self,
        user_query: str,
        structured_data: StructuredData | None = None,
        rag_results: RAGResults | None = None,
    ) -> FinalAnswer:
        """Generate a final answer from Structured Data Analysis results."""
        try:
            analysis = structured_data.text if structured_data else None
            retrieved_documents = rag_results.documents if rag_results else None

            # Prepare request payload for summarizer API
            payload = {"query": user_query}
            if analysis:
                payload["reference_analysis"] = analysis
            if rag_results:
                payload["relevant_documents"] = retrieved_documents

            # Log the payload
            if self.verbose:
                simplified_payload = {
                    "query": user_query,
                    "reference_analysis": analysis[:200] + "..." if analysis else "None",
                    "relevant_documents": (
                        retrieved_documents[:3] if retrieved_documents else "None"
                    ),
                }
                logger.info(
                    f"Sending payload to Final Answer API: {json.dumps(simplified_payload, indent=2)}"
                )

            # Make API request to summarizer
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    FINAL_ANSWER_API_URL,
                    json=payload,
                    headers={
                        "accept": "application/json",
                        "Content-Type": "application/json",
                    },
                ) as response:
                    response_text = await response.text()

                    if response.status != 200:
                        raise Exception(
                            f"Final Answer API returned status {response.status}: {response_text}"
                        )

                    try:
                        result = json.loads(response_text)
                    except json.JSONDecodeError as e:
                        raise Exception(
                            f"Failed to parse API response: {e}\nResponse text: {response_text}"
                        )

                    # Log the response
                    if self.verbose:
                        simplified_response = {
                            "answer": result.get("answer", "N/A")[:200] + "...",
                            "metadata": {
                                "prompt": result.get("metadata", {}).get(
                                    "prompt", "N/A"
                                )[:200]
                                + "...",
                            },
                        }
                        logger.info(
                            f"Final Answer API Response: {json.dumps(simplified_response, indent=2)}"
                        )

                    # Create summary with metadata
                    final_answer = FinalAnswer(
                        text=result["answer"].strip(),
                    )

                    return final_answer

        except Exception as e:
            logger.error(f"Error generating final answer: {str(e)}")
            raise
