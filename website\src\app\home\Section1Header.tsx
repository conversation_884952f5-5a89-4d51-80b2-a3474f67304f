import React from "react";
import { Box, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";

interface Section1HeaderProps {
    children?: React.ReactNode;
}

const Section1Header: React.FC<Section1HeaderProps> = () => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();

    return (
        <Box sx={{ textAlign: "center", my: isMobile ? 1 : 3 }}>
            <Typography variant={"h1"}>Bringing Evidence to Scale</Typography>
            <Typography
                variant={isMobile ? "body1" : isTablet ? "body1" : "body1"}
                sx={{
                    textAlign: "center",
                    color: `${theme.palette.primary.main} !important`,
                }}
            >
                Quantified impact. Interactive findings. Smarter decisions.
            </Typography>
        </Box>
    );
};

export default Section1Header;