from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
import os
from typing import AsyncGenerator
import logging
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

db_host = os.getenv("MYSQL_HOST")
db_name = os.getenv("MYSQL_CORE_DATABASE", "impactai-db")
db_user = os.getenv("MYSQL_USER")
db_password = os.getenv("MYSQL_PASSWORD")

DATABASE_URL = f"mysql+aiomysql://{db_user}:{db_password}@{db_host}/{db_name}"

engine = create_async_engine(
    DATABASE_URL,
    echo=False,
    pool_size=10,
    max_overflow=10,
    pool_timeout=30,
    pool_recycle=1800,
    pool_pre_ping=False,
)

AsyncSessionLocal = async_sessionmaker(
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False,
)


@asynccontextmanager
async def get_session() -> AsyncGenerator[AsyncSession, None]:
    session = AsyncSessionLocal()
    try:
        yield session
    except Exception as e:
        logger.error(f"Session error: {e}")
        await session.rollback()
        raise
    finally:
        await session.close()
        logger.debug("Session closed, connection returned to pool")


async def get_core_db_session():
    async with get_session() as session:
        yield session
