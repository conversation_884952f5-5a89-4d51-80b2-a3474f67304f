import React from "react";
import CloseIcon from "@mui/icons-material/Close";
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import ArticleOutlinedIcon from '@mui/icons-material/ArticleOutlined';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import {
  Box,
  Card,
  CardContent,
  IconButton,
  Typography,
  Link,
  Chip,
  Collapse,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useRef, useState, useCallback } from "react";
import { Source } from "../../../types/ConversationTypes";

interface SourcesProps {
  onClose: () => void;
  sources: Source[] | undefined | null;
  messageId: string | null;
  selectedStudy: string;
  activeSourcePaperIds: string[];
  activeSourceMessageId: string | null;
  displayCloseButton?: boolean;
  headerVariant?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "subtitle1" | "subtitle2" | "body1" | "body2" | "caption" | "button" | "overline" | "inherit";
  headerFontSize?: number | string;
  headerFontWeight?: number | string;
  onClearFilter?: () => void;
}

const Sources: React.FC<SourcesProps> = ({
  onClose,
  sources,
  messageId,
  selectedStudy,
  activeSourcePaperIds,
  activeSourceMessageId,
  displayCloseButton = true,
  headerVariant = "h6",
  headerFontSize,
  headerFontWeight,
  onClearFilter
}) => {
  const [currentSources, setCurrentSources] = useState<Source[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedSources, setExpandedSources] = useState<Set<string>>(new Set());
  const isFiltered = messageId === activeSourceMessageId && activeSourcePaperIds.length > 0;
  const theme = useTheme();
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Toggle expansion for individual sources
  const toggleSourceExpansion = useCallback((sourceId: string) => {
    setExpandedSources(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sourceId)) {
        newSet.delete(sourceId);
      } else {
        newSet.add(sourceId);
      }
      return newSet;
    });
  }, []);

  // Check if source is expanded
  const isSourceExpanded = useCallback((sourceId: string) => {
    return expandedSources.has(sourceId);
  }, [expandedSources]);

  const selectedCardStyle = {
    borderColor: theme.palette.primary.light,
    borderWidth: 1,
  };
  const handleShowAll = () => {
    onClearFilter && onClearFilter();
  };


  useEffect(() => {
    if (messageId) {
      console.log('messageId received', messageId)
      if (sources) {
        console.log('sources has length', sources)
        let validSources = sources.filter(
          (item) => item.citation?.trim() || item.title?.trim()
        ).sort((b, a) => {
          if (a.position && b.position) {
            return b.position - a.position;
          }
          return 1;
        });

        if (isFiltered && activeSourcePaperIds.length > 0) {
          validSources = validSources.filter(source =>
            source.paper_id && activeSourcePaperIds.includes(source.paper_id)
          );
        }

        setCurrentSources(validSources);
        setLoading(false);
        setError(validSources.length === 0 ? "No valid sources available for this message." : null);

        const selectedIndex = validSources.findIndex(
          (item) => item.paper_id === selectedStudy
        );
        if (selectedIndex !== -1) {
          setTimeout(() => {
            if (cardRefs.current[selectedIndex]) {
              cardRefs.current[selectedIndex]?.scrollIntoView({
                behavior: "smooth",
                block: "center",
              });
            }
          }, 0);
        }
      } else if (sources === null) {
        setLoading(false);
        setError('No sources available for this message.');
      } else {
        setLoading(true);
        setError(null);
      }
    } else {
      setCurrentSources([]);
      setLoading(false);
      setError(null);
    }
  }, [messageId, sources, selectedStudy, isFiltered, activeSourcePaperIds]);



  const generateLink = useCallback((url: string | undefined): string | undefined => {
    if (!url) return undefined;
    if (url.startsWith('http')) {
      return url;
    } else {
      return `https://doi.org/${url}`;
    }
  }, []);

  const setCardRef = useCallback((el: HTMLDivElement | null, index: number) => {
    if (el) {
      cardRefs.current[index] = el;
    }
  }, []);

  return (
    <>
      {currentSources && currentSources.length > 0 ? (
        <Card
          elevation={0}
          sx={{
            width: '100%',
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: '8px',
            display: 'flex',
            flexDirection: 'column',
            maxHeight: "100%",
            background: theme.palette.common.white,
          }}
        >
          <Box
            sx={{
              p: 1.5,
              background: theme.palette.common.white,
              zIndex: 1,
              flexShrink: 0,
            }}
          >
            {/* Header row with title and close button */}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 0.5, // Reduced margin to bring pill closer to header as per Figma design
              }}
            >
              <Typography
                variant={headerVariant}
                component="div"
                sx={{
                  color: theme.palette.text.primary,
                  fontSize: headerFontSize || '20px',
                  fontWeight: 500,
                  lineHeight: 1.6,
                  letterSpacing: '0.15px'
                }}
              >
                Sources
              </Typography>
              {displayCloseButton && (
                <IconButton onClick={onClose} size="small" sx={{ color: theme.palette.text.secondary }}>
                  <CloseIcon />
                </IconButton>
              )}
            </Box>

            {/* Sources count pill positioned under the close button */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 1 }}> {/* Added margin bottom for spacing before sources list */}
              {(() => {
                const totalCount = sources?.filter(
                  (item) => item.citation?.trim() || item.title?.trim()
                ).length || 0;
                const filteredCount = currentSources.length;
                if (isFiltered) {
                  return (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        borderRadius: '16px',
                        background: theme.palette.common.white,
                        border: `1px solid ${theme.palette.divider}`,
                        px: 2,
                        py: 0.5,
                        fontSize: 13,
                        fontWeight: 500,
                        color: theme.palette.text.primary,
                        boxShadow: 'none',
                        gap: 1,
                        minHeight: '28px',
                      }}
                    >
                      {`${filteredCount} of ${totalCount} sources`}
                      <IconButton
                        size="small"
                        onClick={handleShowAll}
                        sx={{ ml: 0.5, p: 0.5, color: theme.palette.text.secondary }}
                        aria-label="Clear filter"
                      >
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  );
                } else if (totalCount > 0) {
                  return (
                    <Box
                      sx={{
                        borderRadius: '16px',
                        background: 'rgba(59, 130, 246, 0.1)',
                        border: `1px solid rgba(59, 130, 246, 0.2)`,
                        px: 2,
                        py: 0.5,
                        fontSize: 13,
                        fontWeight: 500,
                        color: '#3B82F6',
                        minHeight: '28px',
                      }}
                    >
                      {`${totalCount} sources`}
                    </Box>
                  );
                }
                return null;
              })()}
            </Box>
          </Box>

          <Box
            id="source-list-scroll"
            sx={{
              overflowY: 'auto',
              flexGrow: 1,
              p: 1.5,
              pt: 1,
            }}
          >
            <AnimatePresence initial={false}>
              {currentSources.map((item, index) => {
                const isSelected = selectedStudy === item.paper_id;
                const isExpanded = isSourceExpanded(item.id);

                return (
                  <motion.div
                    key={index}
                    style={{
                      overflow: 'hidden',
                      marginBottom: theme.spacing(1.5),
                    }}
                    ref={(el) => setCardRef(el, index)}
                  >
                    <Card
                      variant="outlined"
                      sx={{
                        border: `1px solid ${theme.palette.divider}`,
                        background: theme.palette.background.default,
                        ...(isSelected ? selectedCardStyle : {}),
                      }}
                    >
                      <CardContent
                        sx={{
                          padding: '12px !important',
                          '&:last-child': { paddingBottom: '12px !important' },
                        }}
                      >
                        {/* Header Section */}
                        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, mb: 1 }}>
                          <Box sx={{ flex: '0 0 auto', width: '24px', mt: 0.25 }}>
                            <ArticleOutlinedIcon sx={{ width: 20, height: 20, color: theme.palette.text.secondary }} />
                          </Box>
                          <Box sx={{ flex: '1 1 auto', overflow: 'hidden' }}>
                            {item?.title && (
                              <Typography
                                variant="body2"
                                sx={{
                                  fontWeight: 500,  // Restored to normal weight
                                  color: theme.palette.text.primary,
                                  fontSize: '14px',
                                  lineHeight: 1.4,
                                  mb: 0.5,
                                }}
                              >
                                {item.title}
                              </Typography>
                            )}
                            {item?.citation && (
                              <Typography
                                variant="body2"
                                sx={{
                                  color: theme.palette.text.secondary,
                                  fontSize: '12px',
                                  lineHeight: 1.3,
                                  mb: 0.5,
                                }}
                              >
                                {item.citation}
                              </Typography>
                            )}
                            {item?.journal_name && (
                              <Typography
                                variant="body2"
                                sx={{
                                  color: theme.palette.text.secondary,
                                  fontSize: '12px',
                                  fontStyle: 'italic',
                                  lineHeight: 1.3,
                                }}
                              >
                                {item.journal_name}
                              </Typography>
                            )}
                          </Box>
                          <Box sx={{ flex: '0 0 auto', display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            {item?.doi_url && (
                              <Link
                                href={generateLink(item.doi_url || undefined)}
                                target="_blank"
                                rel="noopener noreferrer"
                                sx={{ color: theme.palette.primary.main }}
                                onClick={(e) => e.stopPropagation()}
                              >
                                <OpenInNewIcon sx={{ width: 16, height: 16, color: theme.palette.text.secondary }} />
                              </Link>
                            )}
                          </Box>
                        </Box>

                        {/* Abstract Section */}
                        {item?.abstract && (
                          <Box sx={{ mb: 1, ml: '32px' }}> {/* Added left margin to align with title text */}
                            <Typography
                              variant="body2"
                              sx={{
                                color: theme.palette.text.secondary,
                                fontSize: '12px',
                                lineHeight: 1.4,
                              }}
                            >
                              {item.abstract}
                            </Typography>
                          </Box>
                        )}

                        {/* Tags Section */}
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1, ml: '32px' }}> {/* Added left margin to align with title text */}
                          {item?.country && (
                            <Chip
                              label={`Country: ${item.country}`}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '10px', height: '20px' }}
                            />
                          )}
                          {item?.region && (
                            <Chip
                              label={`Region: ${item.region}`}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '10px', height: '20px' }}
                            />
                          )}
                          {item?.income_group && (
                            <Chip
                              label={`Income Group: ${item.income_group}`}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '10px', height: '20px' }}
                            />
                          )}
                          {item?.quality_score_category && (
                            <Chip
                              label={`Quality Score: ${item.quality_score_category}`}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '10px', height: '20px' }}
                            />
                          )}
                          {item?.sector && (
                            <Chip
                              label={`Sector: ${item.sector}`}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '10px', height: '20px' }}
                            />
                          )}
                        </Box>

                        {/* Expand/Collapse Button */}
                        {(item?.intervention_name || item?.outcome_name) && (
                          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                            <IconButton
                              size="small"
                              onClick={() => toggleSourceExpansion(item.id)}
                              sx={{
                                color: theme.palette.text.secondary,
                                '&:hover': { backgroundColor: theme.palette.action.hover }
                              }}
                            >
                              {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                            </IconButton>
                          </Box>
                        )}

                        {/* Expandable Content */}
                        <Collapse in={isExpanded}>
                          <Box sx={{ mt: 1, pt: 1, borderTop: `1px solid ${theme.palette.divider}`, ml: '32px' }}> {/* Added left margin to align with title text */}
                            {item?.intervention_name && (
                              <Box sx={{ mb: 2 }}>
                                <Typography
                                  variant="subtitle2"
                                  sx={{
                                    fontWeight: 600,
                                    color: theme.palette.text.primary,
                                    fontSize: '12px',
                                    mb: 0.5,
                                  }}
                                >
                                  Intervention name
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: theme.palette.text.secondary,
                                    fontSize: '11px',
                                    lineHeight: 1.4,
                                  }}
                                >
                                  • {item.intervention_name}
                                </Typography>
                                {item?.intervention_details && (
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: theme.palette.text.secondary,
                                      fontSize: '11px',
                                      lineHeight: 1.4,
                                      mt: 0.5,
                                    }}
                                  >
                                    • {item.intervention_details}
                                  </Typography>
                                )}
                              </Box>
                            )}

                            {item?.outcome_name && (
                              <Box>
                                <Typography
                                  variant="subtitle2"
                                  sx={{
                                    fontWeight: 600,
                                    color: theme.palette.text.primary,
                                    fontSize: '12px',
                                    mb: 0.5,
                                  }}
                                >
                                  Outcome name
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: theme.palette.text.secondary,
                                    fontSize: '11px',
                                    lineHeight: 1.4,
                                  }}
                                >
                                  • {item.outcome_name}
                                </Typography>
                                {item?.outcome_details && (
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: theme.palette.text.secondary,
                                      fontSize: '11px',
                                      lineHeight: 1.4,
                                      mt: 0.5,
                                    }}
                                  >
                                    • {item.outcome_details}
                                  </Typography>
                                )}
                              </Box>
                            )}
                          </Box>
                        </Collapse>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </Box>
        </Card>

      ) : null}
    </>

  );
};

export default Sources;
