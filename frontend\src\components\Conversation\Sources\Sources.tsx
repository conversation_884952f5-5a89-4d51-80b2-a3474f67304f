import React from "react";
import CloseIcon from "@mui/icons-material/Close";
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import ArticleOutlinedIcon from '@mui/icons-material/ArticleOutlined';
import {
  Box,
  Card,
  CardContent,
  IconButton,
  Typography,
  Link,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useRef, useState, useCallback } from "react";
import { Source } from "../../../types/ConversationTypes";

interface SourcesProps {
  onClose: () => void;
  sources: Source[] | undefined | null;
  messageId: string | null;
  selectedStudy: string;
  activeSourcePaperIds: string[];
  activeSourceMessageId: string | null;
  displayCloseButton?: boolean;
  headerVariant?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "subtitle1" | "subtitle2" | "body1" | "body2" | "caption" | "button" | "overline" | "inherit";
  headerFontSize?: number | string;
  headerFontWeight?: number | string;
  onClearFilter?: () => void;
}

const Sources: React.FC<SourcesProps> = ({
  onClose,
  sources,
  messageId,
  selectedStudy,
  activeSourcePaperIds,
  activeSourceMessageId,
  displayCloseButton = true,
  headerVariant = "h6",
  headerFontSize,
  headerFontWeight,
  onClearFilter
}) => {
  const [currentSources, setCurrentSources] = useState<Source[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const isFiltered = messageId === activeSourceMessageId && activeSourcePaperIds.length > 0;
  const theme = useTheme();
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);
  const lineHeight = 20;
  const supportsWebKitLineClamp = typeof CSS !== 'undefined' && CSS.supports('-webkit-line-clamp', '2');

  const selectedCardStyle = {
    borderColor: theme.palette.primary.light,
    borderWidth: 1,
  };
  const handleShowAll = () => {
    onClearFilter && onClearFilter();
  };


  useEffect(() => {
    if (messageId) {
      console.log('messageId received', messageId)
      if (sources) {
        console.log('sources has length', sources)
        let validSources = sources.filter(
          (item) => item.citation?.trim() || item.title?.trim()
        ).sort((b, a) => {
          if (a.position && b.position) {
            return b.position - a.position;
          }
          return 1;
        });

        if (isFiltered && activeSourcePaperIds.length > 0) {
          validSources = validSources.filter(source =>
            source.paper_id && activeSourcePaperIds.includes(source.paper_id)
          );
        }

        setCurrentSources(validSources);
        setLoading(false);
        setError(validSources.length === 0 ? "No valid sources available for this message." : null);

        const selectedIndex = validSources.findIndex(
          (item) => item.paper_id === selectedStudy
        );
        if (selectedIndex !== -1) {
          setTimeout(() => {
            if (cardRefs.current[selectedIndex]) {
              cardRefs.current[selectedIndex]?.scrollIntoView({
                behavior: "smooth",
                block: "center",
              });
            }
          }, 0);
        }
      } else if (sources === null) {
        setLoading(false);
        setError('No sources available for this message.');
      } else {
        setLoading(true);
        setError(null);
      }
    } else {
      setCurrentSources([]);
      setLoading(false);
      setError(null);
    }
  }, [messageId, sources, selectedStudy, isFiltered, activeSourcePaperIds]);

  const truncateText = useCallback((text: string | undefined, lines: number = 1, lineHeight: number = 20): string => {
    if (!text) return "";
    const maxHeight = lines * lineHeight;
    const element = document.createElement('div');
    element.style.width = '100%';
    element.style.wordBreak = 'break-word';
    element.style.overflow = 'hidden';
    element.style.maxHeight = `${maxHeight}px`;
    element.style.lineHeight = `${lineHeight}px`;
    element.textContent = text;
    document.body.appendChild(element);
    let truncated = text;
    if (element.offsetHeight < element.scrollHeight) {
      while (element.offsetHeight < element.scrollHeight && truncated.length > 0) {
        truncated = truncated.slice(0, -1);
        element.textContent = truncated + '...';
      }
    }
    document.body.removeChild(element);
    return truncated;
  }, []);

  const generateLink = useCallback((url: string | undefined): string | undefined => {
    if (!url) return undefined;
    if (url.startsWith('http')) {
      return url;
    } else {
      return `https://doi.org/${url}`;
    }
  }, []);

  const setCardRef = useCallback((el: HTMLDivElement | null, index: number) => {
    if (el) {
      cardRefs.current[index] = el;
    }
  }, []);

  return (
    <>
      {currentSources && currentSources.length > 0 ? (
        <Card
          elevation={0}
          sx={{
            width: '100%',
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: '8px',
            display: 'flex',
            flexDirection: 'column',
            maxHeight: "100%",
            background: theme.palette.common.white,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              p: 2,
              background: theme.palette.common.white,
              zIndex: 1,
              flexShrink: 0,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography
                variant={headerVariant}
                component="div"
                sx={{
                  color: theme.palette.text.primary,
                  fontSize: headerFontSize,
                  fontWeight: headerFontWeight
                }}
              >
                Sources
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {(() => {
                const totalCount = sources?.filter(
                  (item) => item.citation?.trim() || item.title?.trim()
                ).length || 0;
                const filteredCount = currentSources.length;
                if (isFiltered) {
                  return (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        borderRadius: '16px',
                        background: theme.palette.common.white,
                        border: `1px solid ${theme.palette.divider}`,
                        px: 2,
                        py: 0.5,
                        fontSize: 13,
                        fontWeight: 500,
                        color: theme.palette.text.primary,
                        boxShadow: 'none',
                        gap: 1,
                        minHeight: '28px',
                        mr: 1,
                      }}
                    >
                      {`${filteredCount} of ${totalCount} sources`}
                      <IconButton
                        size="small"
                        onClick={handleShowAll}
                        sx={{ ml: 0.5, p: 0.5, color: theme.palette.text.secondary }}
                        aria-label="Clear filter"
                      >
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  );
                } else if (totalCount > 0) {
                  return (
                    <Box
                      sx={{
                        borderRadius: '16px',
                        background: 'transparent',
                        px: 2,
                        py: 0.5,
                        fontSize: 13,
                        fontWeight: 500,
                        color: theme.palette.text.secondary,
                        minHeight: '28px',
                        mr: 1,
                      }}
                    >
                      {`${totalCount} sources`}
                    </Box>
                  );
                }
                return null;
              })()}
              {displayCloseButton && (
                <IconButton onClick={onClose} size="small" sx={{ color: theme.palette.text.secondary }}>
                  <CloseIcon />
                </IconButton>
              )}
            </Box>
          </Box>
          <Box sx={{ px: 2, pb: 0 }}>
            <Box sx={{ borderBottom: `1px solid ${theme.palette.divider}` }} />
          </Box>
          <Box
            id="source-list-scroll"
            sx={{
              overflowY: 'auto',
              flexGrow: 1,
              p: 2,
              pt: 1.5,
            }}
          >
            <AnimatePresence initial={false}>
              {currentSources.map((item, index) => {
                const isSelected = selectedStudy === item.paper_id;
                // Use string | undefined for generateLink
                const linkUrl = generateLink(item?.doi_url || undefined);

                return (
                  <motion.div
                    key={index}
                    style={{
                      overflow: 'hidden',
                      marginBottom: theme.spacing(2),
                    }}
                    ref={(el) => setCardRef(el, index)}
                  >
                    <Card
                      variant="outlined"
                      sx={{
                        border: `1px solid ${theme.palette.divider}`,
                        background: theme.palette.background.default,
                        ...(isSelected ? selectedCardStyle : {}),
                      }}
                    >
                      <CardContent
                        sx={{
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'flex-start',
                          gap: 1,
                          padding: '16px !important',
                          width: '100%',
                          minHeight: `calc(32px + ${3 * lineHeight}px)`,
                        }}
                      >
                        <Box sx={{ flex: '0 0 auto', width: '30px' }}>
                          <ArticleOutlinedIcon sx={{ width: 20, height: 20, color: theme.palette.text.secondary }} />
                        </Box>
                        <Box
                          sx={{
                            flex: '1 1 auto',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'flex-start',
                            gap: '4px',
                            overflow: 'hidden',
                            width: 'calc(100% - 60px)',
                          }}
                        >
                          {item?.title ? (
                            <Typography
                              variant="caption"
                              fontSize={12}
                              sx={{
                                overflowWrap: 'break-word',
                                width: '100%',
                                lineHeight: `${lineHeight}px`,
                                maxHeight: `${1 * lineHeight}px`,
                                WebkitLineClamp: supportsWebKitLineClamp ? 1 : undefined,
                                WebkitBoxOrient: supportsWebKitLineClamp ? 'vertical' : undefined,
                                display: supportsWebKitLineClamp ? '-webkit-box' : undefined,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                fontWeight: '500',
                                color: theme.palette.text.primary,
                              }}
                            >
                              {supportsWebKitLineClamp ? item.title : truncateText(item.title, 1, lineHeight)}
                            </Typography>
                          ) : (
                            <Box sx={{ height: `${lineHeight}px` }} />
                          )}

                          {item?.citation ? (
                            <Typography
                              variant="body2"
                              fontSize={12}
                              sx={{
                                overflowWrap: 'break-word',
                                width: '100%',
                                lineHeight: `${lineHeight}px`,
                                maxHeight: `${1 * lineHeight}px`,
                                WebkitLineClamp: supportsWebKitLineClamp ? 1 : undefined,
                                WebkitBoxOrient: supportsWebKitLineClamp ? 'vertical' : undefined,
                                display: supportsWebKitLineClamp ? '-webkit-box' : undefined,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                color: theme.palette.text.primary,
                              }}
                            >
                              {supportsWebKitLineClamp ? item.citation : truncateText(item.citation, 1, lineHeight)}
                            </Typography>
                          ) : (
                            <Box sx={{ height: `${lineHeight}px` }} />
                          )}

                          {item?.journal_name ? (
                            <Typography
                              variant="body2"
                              fontSize={12}
                              sx={{
                                overflowWrap: 'break-word',
                                width: '100%',
                                lineHeight: `${lineHeight}px`,
                                maxHeight: `${1 * lineHeight}px`,
                                WebkitLineClamp: supportsWebKitLineClamp ? 1 : undefined,
                                WebkitBoxOrient: supportsWebKitLineClamp ? 'vertical' : undefined,
                                display: supportsWebKitLineClamp ? '-webkit-box' : undefined,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                color: theme.palette.text.primary,
                              }}
                            >
                              {supportsWebKitLineClamp ? item.journal_name : truncateText(item.journal_name, 1, lineHeight)}
                            </Typography>
                          ) : (
                            <Box sx={{ height: `${lineHeight}px` }} />
                          )}
                        </Box>
                        <Box
                          sx={{
                            flex: '0 0 auto',
                            width: '30px',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        >
                          {item?.doi_url && (
                            <Link
                              href={generateLink(item.doi_url || undefined)}
                              target="_blank"
                              rel="noopener noreferrer"
                              sx={{ color: theme.palette.primary.main }}
                              onClick={(e) => e.stopPropagation()}
                            >
                              <OpenInNewIcon sx={{ width: 20, height: 20, color: theme.palette.text.secondary }} />
                            </Link>
                          )}
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </Box>
        </Card>

      ) : null}
    </>

  );
};

export default Sources;
