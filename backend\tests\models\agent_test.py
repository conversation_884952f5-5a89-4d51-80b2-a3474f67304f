import json

from models.agent import RawDataUsed, Tool<PERSON><PERSON>, AgentResponse, ResponseContext
from models.plot import TagWithLevel
from utils.filesystem import get_file_path


def load_mock_data():
    file_path = get_file_path("mock-agent-responses.json")
    with open(file_path, "r") as f:
        return json.load(f)


MOCK_DATA = load_mock_data()
MOCK_RESPONSE = MOCK_DATA[0]["response"]


class TestRawDataUsed:
    """Test suite for RawDataUsed class methods and properties"""

    def setup_method(self):
        """Setup for each test"""
        self.raw_data = RawDataUsed(
            paper_id="test123",
            paper_combined_id="T123",
            title="Test Paper",
            year=2023,
            authors="Test Author",
            income_group="High Income",
            region="North America",
            intervention_tag_labels="education:technology:computer-aided",
            outcome_tag_labels="learning:mathematics:algebra",
            intervention_tag_short_labels="edu,tech,cai",
            intervention_id="1,2,3",
            outcome_tag_short_labels="learn,math,alg",
            outcome_ids="4,5,6",
            cohen_d=0.5,
            hedges_d=0.45,
            standardized_ci_lower=0.2,
            standardized_ci_upper=0.7,
        )

    def test_intervention_tags_with_levels(self):
        """Test intervention tags are correctly parsed with levels"""
        result = self.raw_data.intervention_tags_with_levels
        assert len(result) == 3
        assert result[0] == TagWithLevel(tag_label="education", level=0)
        assert result[1] == TagWithLevel(tag_label="technology", level=1)
        assert result[2] == TagWithLevel(tag_label="computer-aided", level=2)

    def test_outcome_tags_with_levels(self):
        """Test outcome tags are correctly parsed with levels"""
        result = self.raw_data.outcome_tags_with_levels
        assert len(result) == 3
        assert result[0] == TagWithLevel(tag_label="learning", level=0)
        assert result[1] == TagWithLevel(tag_label="mathematics", level=1)
        assert result[2] == TagWithLevel(tag_label="algebra", level=2)

    def test_income_group_code(self):
        """Test income group code generation"""
        assert self.raw_data.income_group_code == "HI"
        self.raw_data.income_group = None
        assert self.raw_data.income_group_code is None

    def test_region_code(self):
        """Test region code generation"""
        assert self.raw_data.region_code == "NA"
        self.raw_data.region = None
        assert self.raw_data.region_code is None

    def test_citation(self):
        """Test citation string generation"""
        assert self.raw_data.citation == "Test Author (2023)"

    def test_parsed_intervention_tag_ids(self):
        """Test parsing of intervention tag IDs"""
        self.raw_data.intervention_tag_ids = "1, 2, 3"
        assert self.raw_data.parsed_intervention_tag_ids == [1, 2, 3]
        self.raw_data.intervention_tag_ids = None
        assert self.raw_data.parsed_intervention_tag_ids is None

    def test_parsed_outcome_tag_ids(self):
        """Test parsing of outcome tag IDs"""
        self.raw_data.outcome_tag_ids = "4, 5, 6"
        assert self.raw_data.parsed_outcome_tag_ids == [4, 5, 6]
        self.raw_data.outcome_tag_ids = None
        assert self.raw_data.parsed_outcome_tag_ids is None

    def test_score(self):
        """Test score generation with both cohen_d and hedges_d"""
        # Test with cohen_d
        score = self.raw_data.score
        assert score.value == 0.5  # Uses cohen_d
        assert score.lower == 0.2
        assert score.upper == 0.7

        # Test with hedges_d only
        self.raw_data.cohen_d = None
        score = self.raw_data.score
        assert score.value == 0.45  # Uses hedges_d
        assert score.lower == 0.2
        assert score.upper == 0.7

    def test_extract_map(self):
        """Test extraction of ID-label mappings"""
        result = self.raw_data._extract_map("a,b,c", "1,2,3")
        assert result == {"1": "a", "2": "b", "3": "c"}

        # Test empty inputs
        assert self.raw_data._extract_map("", "") == {}
        assert self.raw_data._extract_map(None, None) == {}

        # Test mismatched lengths
        assert self.raw_data._extract_map("a,b", "1,2,3") == {}

    def test_extracted_intervention_map(self):
        """Test extraction of intervention mappings"""
        result = self.raw_data.extracted_intervention_map()
        assert result == {"1": "edu", "2": "tech", "3": "cai"}

        # Test with empty data
        self.raw_data.intervention_tag_short_labels = None
        assert self.raw_data.extracted_intervention_map() == {}

    def test_extracted_outcome_map(self):
        """Test extraction of outcome mappings"""
        result = self.raw_data.extracted_outcome_map()
        assert result == {"4": "learn", "5": "math", "6": "alg"}

        # Test with empty data
        self.raw_data.outcome_tag_short_labels = None
        assert self.raw_data.extracted_outcome_map() == {}


class TestToolData:
    """Test suite for ToolData class methods and properties"""

    def setup_method(self):
        """Setup for each test using mock data"""
        mock_response = MOCK_RESPONSE
        self.tool_data = ToolData(
            data_used=[
                RawDataUsed(**mock_response["context"]["tool_data"]["data_used"][0])
            ]
        )

    def test_flat_effect_sizes(self):
        """Test generation of flat effect sizes"""
        result = self.tool_data.flat_effect_sizes
        assert len(result) == 1
        assert result[0].paper_id == self.tool_data.data_used[0].paper_id

    def test_unique_studies(self):
        """Test unique studies extraction"""
        # Add duplicate study
        self.tool_data.data_used.append(self.tool_data.data_used[0])
        result = list(self.tool_data.unique_studies)
        assert len(result) == 1
        assert result[0].paper_id == self.tool_data.data_used[0].paper_id

    def test_sources(self):
        """Test sources generation"""
        result = self.tool_data.sources
        assert len(result) > 0
        assert result[0]["paper_id"] == self.tool_data.data_used[0].paper_id

    def test_plot_studies(self):
        """Test plot studies generation"""
        result = self.tool_data.plot_studies
        assert len(result) > 0
        assert result[0].id == self.tool_data.data_used[0].paper_id


class TestAgentResponse:
    """Test suite for AgentResponse class methods and properties"""

    def setup_method(self):
        """Setup for each test using mock data"""
        mock_response = MOCK_RESPONSE
        self.agent_response = AgentResponse(
            response=mock_response["response"],
            context=ResponseContext(
                query=mock_response["context"]["query"],
                conversation_id=mock_response["context"]["conversation_id"],
                tool_data=ToolData(
                    data_used=[
                        RawDataUsed(
                            **mock_response["context"]["tool_data"]["data_used"][0]
                        )
                    ]
                ),
            ),
        )

    def test_has_plot_data(self):
        """Test plot data presence check"""
        assert self.agent_response.has_plot_data() is True
        self.agent_response.context.tool_data.data_used = []
        assert self.agent_response.has_plot_data() is False

    def test_has_sources(self):
        """Test sources presence check"""
        assert self.agent_response.has_sources() is True
        self.agent_response.context.tool_data.data_used = []
        assert self.agent_response.has_sources() is False

    def test_sources_data(self):
        """Test sources data generation"""
        result = self.agent_response.sources_data()
        assert len(result) > 0
        # Verify source priority ordering
        priority_source = result[0]
        assert "paper_id" in priority_source
        assert "citation" in priority_source

    def test_plot_data(self):
        """Test plot data generation"""
        result = self.agent_response.plot_data()
        assert result.type is not None
        assert result.data is not None
        assert len(result.data.studies) > 0

    def test_summary_sources(self):
        """Test summary sources extraction"""
        sources = self.agent_response.summary_sources
        assert isinstance(sources, list)
        # The mock response contains references like [E132], [V56], etc.
        assert len(sources) > 0
        assert all(isinstance(s, str) for s in sources)

    def test_formatted_summary(self):
        """Test formatted summary generation"""
        result = self.agent_response.formated_summary
        assert isinstance(result, str)
        assert "[" in result and "]" in result  # Contains source references
