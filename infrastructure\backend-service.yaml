apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: ${FULL_SERVICE_NAME}
  namespace: ${PROJECT_ID}
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/ingress-status: all
    autoscaling.knative.dev/target: "4"
    run.googleapis.com/minScale: "1"
    run.googleapis.com/startup-cpu-boost: "true"
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/vpc-access-connector: "projects/${PROJECT_ID}/locations/${REGION}/connectors/impact-ai-vpc-connector"
        run.googleapis.com/vpc-access-egress: "private-ranges-only"
        run.googleapis.com/cloudsql-instances: "${PROJECT_ID}:${REGION}:${CLOUDSQL_INSTANCE}"
        run.googleapis.com/execution-environment: "gen2"
    spec:
      containers:
        - image: "${IMAGE_TAG}"
          env:
            - name: PROJECT_ID
              value: "${PROJECT_ID}"
            - name: MYSQL_HOST
              value: "${MYSQL_HOST}"
            - name: MYSQL_DATABASE
              value: "${MYSQL_DATABASE}"
            - name: MYSQL_USER
              value: "${MYSQL_USER}"
            - name: MYSQL_PASSWORD
              value: "${MYSQL_PASSWORD}"
            - name: REDIS_HOST
              value: "${REDIS_HOST}"
            - name: REDIS_PORT
              value: "${REDIS_PORT}"
            - name: GOOGLE_API_KEY
              value: "${GOOGLE_API_KEY}"
            - name: USE_GEMINI
              value: "${USE_GEMINI}"
            - name: RESEND_API_KEY
              value: "${RESEND_API_KEY}"
            - name: DEBUG
              value: "${DEBUG}"
            - name: USE_SQL_PROXY
              value: "${USE_SQL_PROXY}"
          ports:
            - containerPort: 8000
          resources:
            limits:
              memory: "8Gi"
              cpu: "4"
            requests:
              cpu: "2"
              memory: "8Gi"
          startupProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 5
            periodSeconds: 10
            failureThreshold: 3
      containerConcurrency: 10
      timeoutSeconds: 300