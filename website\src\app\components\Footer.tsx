import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import Image from 'next/image';
import { useIsMobile, useIsTablet } from '../components/MobileUtils';

const Footer = () => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();

    const iconStyles = {
        borderRadius: "50%",
        width: "40px",
        height: "40px",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
    };
    const linkStyles = {
        color: theme.palette.common.white,
        textDecoration: "none",
        fontSize: "14px",
        fontWeight: "400",
        position: "relative",
        "&::after": {
            content: '""',
            position: "absolute",
            left: 0,
            bottom: "-0.5px",
            width: "10  0%",
            borderBottom: "1px solid currentColor",
            transform: "scaleX(0)",
            transformOrigin: "left",
        },
        "&:hover::after": {
            transform: "scaleX(1)",
        },
    };
    const boldLinkStyles = {
        color: theme.palette.common.white,
        textDecoration: "none",
        fontSize: "16px",
        fontWeight: "700",
        position: "relative",
        "&::after": {
            content: '""',
            position: "absolute",
            left: 0,
            bottom: "-0.5px",
            width: "100%",
            borderBottom: "1px solid currentColor",
            transform: "scaleX(0)",
            transformOrigin: "left",
        },
        "&:hover::after": {
            transform: "scaleX(1)",
        },
    };

    const handleSignupButtonClick = () => {
        window.location.href = 'https://www.worldbank.org/en/newsletter-subscription';
    };

    const renderDesktopView = () => {
        return (
            <Box
                component="footer"
                sx={{
                    display: "flex",
                    padding: "0px 48px",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "flex-start",
                    alignSelf: "stretch",
                    background: "#012740",
                }}
            >
                {/* Top Row */}
                <Box
                    sx={{
                        padding: "44px 0.5px 44px 0px",
                        display: "flex",
                        width: "100%",
                        justifyContent: "space-between",
                        alignItems: 'flex-start'
                    }}
                >
                    {/* Left Section: Image and Links */}
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: "flex-start", gap: '48px', width: '60%' }}>
                        <Box>
                            <img
                                src="/images/header_footer/footer/wb-logo.png"
                                alt="World Bank Logo"
                                style={{ width: "250px", height: "50px" }}
                            />
                        </Box>
                        <Box sx={{ display: "flex", flexDirection: "row", gap: "16px" }}>
                            <Link href="https://www.worldbank.org/en/who-we-are/ibrd" sx={boldLinkStyles}>
                                IBRD
                            </Link>
                            <Link href="https://ida.worldbank.org/en/home" sx={boldLinkStyles}>
                                IDA
                            </Link>
                            <Link href="https://www.ifc.org/en/home" sx={boldLinkStyles}>
                                IFC
                            </Link>
                            <Link href="https://www.miga.org/" sx={boldLinkStyles}>
                                MIGA
                            </Link>
                            <Link href="https://icsid.worldbank.org/" sx={boldLinkStyles}>
                                ICSID
                            </Link>
                        </Box>
                    </Box>

                    {/* Right Section: Social Media Icons */}
                    <Box sx={{ display: "flex", gap: "8px", alignItems: 'center', width: '40%', justifyContent: 'flex-end' }}>
                        <Link href="https://www.facebook.com/worldbank" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/fb.png" alt="Facebook" width={10} height={20} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.whatsapp.com/channel/0029VaeLyAgAojYvg7Wbr91k" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/WhatsApp.png" alt="WhatsApp" width={20} height={21} />
                            </IconButton>
                        </Link>
                        <Link href="https://x.com/worldbank" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/twitter.png" alt="Twitter" width={15} height={16} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.linkedin.com/company/the-world-bank/" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/linkedln.png" alt="LinkedIn" width={16} height={16} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.instagram.com/worldbank/" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/instagram.png" alt="Instagram" width={17} height={19} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.youtube.com/user/WorldBank" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/YouTube.png" alt="YouTube" width={19} height={14} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.flickr.com/photos/worldbank/" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/flickr.png" alt="Flickr" width={20} height={10} />
                            </IconButton>
                        </Link>
                    </Box>
                </Box>

                {/* Center Row (Flex 1) */}
                <Box sx={{ flex: 1, width: "100%", display: "flex", justifyContent: "space-between", alignItems: "flex-start", marginTop: '44px' }}>
                    {/* Column 1 of Center Row (Links Stacked Vertically) */}
                    <Box sx={{ width: '60%', display: 'flex', flexDirection: 'row', justifyContent: "space-between" }}>
                        <Box>
                            <Link href="https://www.worldbank.org/ext/en/who-we-are" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Who We Are</Link>
                            <Link href="https://www.worldbank.org/en/news" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>News</Link>
                            <Link href="https://www.worldbank.org/en/about/careers" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Careers</Link>
                            <Link href="https://www.worldbank.org/en/about/contacts" sx={{ color: theme.palette.common.white, display: 'block' }}>Contact</Link>
                        </Box>

                        {/* Column 2 of Center Row */}
                        <Box>
                            <Link href="https://www.worldbank.org/en/where-we-work" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Countries</Link>
                            <Link href="https://www.worldbank.org/en/topic" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Topics</Link>
                            <Link href="https://projects.worldbank.org/en/projects-operations/projects-home?lang=en" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Projects & Operations</Link>
                            <Link href="https://www.worldbank.org/en/research" sx={{ color: theme.palette.common.white, display: 'block' }}>Research & Publications</Link>
                        </Box>

                        {/* Column 3 of Center Row */}
                        <Box>
                            <Link href="https://www.worldbank.org/en/events/all" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Events</Link>
                            <Link href="https://data.worldbank.org/" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Data</Link>
                            <Link href="https://academy.worldbank.org/en/home" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Knowledge Academy</Link>
                            <Link href="https://scorecard.worldbank.org/en/scorecard/home" sx={{ color: theme.palette.common.white, display: 'block' }}>Results Scorecard</Link>
                        </Box>
                    </Box>

                    {/* Column 4 of Center Row */}
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            width: '38%',
                            paddingLeft: '10%',
                            alignItems: 'flex-end',
                        }}
                    >
                        {/* Wrapper to ensure alignment */}
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                            <Box
                                sx={{
                                    color: theme.palette.common.white,
                                    marginBottom: '16px',
                                    fontSize: '26px',
                                    lineHeight: '36px',
                                    wordWrap: 'break-word',
                                    maxWidth: '100%',
                                    textAlign: 'right'
                                }}
                            >
                                <strong>STAY CURRENT</strong> WITH OUR LATEST DATA & INSIGHTS
                            </Box>
                            <Button
                                variant="contained"
                                sx={{
                                    textTransform: 'capitalize',
                                    padding: '16px 32px',
                                    color: '#004370',
                                    borderRadius: '32px',
                                    fontSize: '16px',
                                    backgroundColor: theme.palette.common.white,
                                    fontWeight: 700,
                                    marginTop: '8px'
                                }}
                                onClick={handleSignupButtonClick}
                            >
                                Sign Up
                            </Button>
                        </Box>
                    </Box>
                </Box>
                {/* Bottom Row */}
                <Box
                    sx={{
                        display: "flex",
                        width: "100%",
                        height: "133px",
                        padding: "44px 0px 72px 0px",
                        alignItems: "center",
                        justifyContent: 'space-between',
                        gap: "248.859px",
                    }}
                >
                    <Typography component="label"
                        sx={{
                            fontFamily: 'HostGrotesk',
                            fontSize: '12px',
                            color: `${theme.palette.common.white} !important`
                        }}>
                        © 2025 World Bank Group. All rights reserved
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'row', gap: '16px' }}>
                        <Link href="https://www.worldbank.org/en/about/legal" sx={linkStyles}>
                            Legal
                        </Link>
                        <Link href="https://www.worldbank.org/en/about/legal/privacy-notice" sx={linkStyles}>
                            Privacy Notice
                        </Link>
                        <Link href="https://www.worldbank.org/en/who-we-are/site-accessibility" sx={linkStyles}>
                            Site Accessibility
                        </Link>
                        <Link href="https://www.worldbank.org/en/access-to-information" sx={linkStyles}>
                            Access to Information
                        </Link>
                        <Link href="https://www.worldbank.org/en/about/legal/scams" sx={linkStyles}>
                            Scam Alert
                        </Link>
                        <Link href="https://wbgcmsprod.microsoftcrmportals.com/en-US/anonymous-users/int-fraud-management/create-new-complaint/" sx={linkStyles}>
                            Report Fraud or Corruption
                        </Link>
                    </Box>
                </Box>
            </Box>
        );
    }

    const renderTabletView = () => {
        return (
            <Box
                component="footer"
                sx={{
                    display: "flex",
                    padding: "0px 48px",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "flex-start",
                    alignSelf: "stretch",
                    background: "#012740",
                }}
            >
                {/* Top Row: Logo and Nav List */}
                <Box
                    sx={{
                        display: "flex",
                        padding: "44px 0.5px 44px 0px",
                        alignItems: "center",
                        width: "100%",
                        justifyContent: "space-evenly",
                    }}
                >
                    {/* Column 1: Image */}
                    <Box>
                        <img
                            src="/images/header_footer/footer/wb-logo.png"
                            alt="World Bank Logo"
                            style={{ width: "250px", height: "50px" }}
                        />
                    </Box>

                    {/* Column 2: List Items with Links */}
                    <Box sx={{ display: "flex", flexDirection: "row", gap: "8px" }}>
                        <Link
                            href="https://www.worldbank.org/en/who-we-are/ibrd"
                            sx={{
                                color: theme.palette.common.white,
                                textDecoration: "none",
                                fontSize: "16px",
                                fontWeight: "700",
                                position: "relative",
                                "&::after": {
                                    content: '""',
                                    position: "absolute",
                                    left: 0,
                                    bottom: "-0.5px",
                                    width: "100%",
                                    borderBottom: "1px solid currentColor",
                                    transform: "scaleX(0)",
                                    transformOrigin: "left",
                                },
                                "&:hover::after": {
                                    transform: "scaleX(1)",
                                },
                            }}
                        >
                            IBRD
                        </Link>
                        <Link
                            href="https://ida.worldbank.org/en/home"
                            sx={{
                                color: theme.palette.common.white,
                                textDecoration: "none",
                                fontSize: "16px",
                                fontWeight: "700",
                                position: "relative",
                                "&::after": {
                                    content: '""',
                                    position: "absolute",
                                    left: 0,
                                    bottom: "-0.5px",
                                    width: "100%",
                                    borderBottom: "1px solid currentColor",
                                    transform: "scaleX(0)",
                                    transformOrigin: "left",
                                },
                                "&:hover::after": {
                                    transform: "scaleX(1)",
                                },
                            }}
                        >
                            IDA
                        </Link>
                        <Link
                            href="https://www.ifc.org/en/home"
                            sx={{
                                color: theme.palette.common.white,
                                textDecoration: "none",
                                fontSize: "16px",
                                fontWeight: "700",
                                position: "relative",
                                "&::after": {
                                    content: '""',
                                    position: "absolute",
                                    left: 0,
                                    bottom: "-0.5px",
                                    width: "100%",
                                    borderBottom: "1px solid currentColor",
                                    transform: "scaleX(0)",
                                    transformOrigin: "left",
                                },
                                "&:hover::after": {
                                    transform: "scaleX(1)",
                                },
                            }}
                        >
                            IFC
                        </Link>
                        <Link
                            href="https://www.miga.org/"
                            sx={{
                                color: theme.palette.common.white,
                                textDecoration: "none",
                                fontSize: "16px",
                                fontWeight: "700",
                                position: "relative",
                                "&::after": {
                                    content: '""',
                                    position: "absolute",
                                    left: 0,
                                    bottom: "-0.5px",
                                    width: "100%",
                                    borderBottom: "1px solid currentColor",
                                    transform: "scaleX(0)",
                                    transformOrigin: "left",
                                },
                                "&:hover::after": {
                                    transform: "scaleX(1)",
                                },
                            }}
                        >
                            MIGA
                        </Link>
                        <Link
                            href="https://icsid.worldbank.org/"
                            sx={{
                                color: theme.palette.common.white,
                                textDecoration: "none",
                                fontSize: "16px",
                                fontWeight: "700",
                                position: "relative",
                                "&::after": {
                                    content: '""',
                                    position: "absolute",
                                    left: 0,
                                    bottom: "-0.5px",
                                    width: "100%",
                                    borderBottom: "1px solid currentColor",
                                    transform: "scaleX(0)",
                                    transformOrigin: "left",
                                },
                                "&:hover::after": {
                                    transform: "scaleX(1)",
                                },
                            }}
                        >
                            ICSID
                        </Link>
                    </Box>
                </Box>

                {/* Second Row: Social Media Icons */}
                <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center' }}>
                    <Box sx={{ display: "flex", gap: "8px", flexWrap: 'wrap', width: '50%' }}>
                        <Link href="https://www.facebook.com/worldbank" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/fb.png" alt="Facebook" width={10} height={20} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.whatsapp.com/channel/0029VaeLyAgAojYvg7Wbr91k" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/WhatsApp.png" alt="WhatsApp" width={20} height={21} />
                            </IconButton>
                        </Link>
                        <Link href="https://x.com/worldbank" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/twitter.png" alt="Twitter" width={15} height={16} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.linkedin.com/company/the-world-bank/" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/linkedln.png" alt="LinkedIn" width={16} height={16} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.instagram.com/worldbank/" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/instagram.png" alt="Instagram" width={17} height={19} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.youtube.com/user/WorldBank" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/YouTube.png" alt="YouTube" width={19} height={14} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.flickr.com/photos/worldbank/" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/flickr.png" alt="Flickr" width={20} height={10} />
                            </IconButton>
                        </Link>
                    </Box>
                </Box>

                {/* Center Row (Flex 1) */}
                <Box sx={{ flex: 1, width: "100%", display: "flex", flexDirection: "column", marginTop: '44px' }}>
                    {/* Links Stacked Vertically and Columns */}
                    <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: "space-between", width: "100%" }}>
                        <Box sx={{ width: '100%', display: 'flex', flexDirection: 'row', justifyContent: "space-between" }}>
                            <Box>
                                <Link href="https://www.worldbank.org/ext/en/who-we-are" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Who We Are</Link>
                                <Link href="https://www.worldbank.org/en/news" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>News</Link>
                                <Link href="https://www.worldbank.org/en/about/careers" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Careers</Link>
                                <Link href="https://www.worldbank.org/en/about/contacts" sx={{ color: theme.palette.common.white, display: 'block' }}>Contact</Link>
                            </Box>

                            {/* Column 2 of Center Row */}
                            <Box>
                                <Link href="https://www.worldbank.org/en/where-we-work" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Countries</Link>
                                <Link href="https://www.worldbank.org/en/topic" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Topics</Link>
                                <Link href="https://projects.worldbank.org/en/projects-operations/projects-home?lang=en" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Projects & Operations</Link>
                                <Link href="https://www.worldbank.org/en/research" sx={{ color: theme.palette.common.white, display: 'block' }}>Research & Publications</Link>
                            </Box>

                            {/* Column 3 of Center Row */}
                            <Box>
                                <Link href="https://www.worldbank.org/en/events/all" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Events</Link>
                                <Link href="https://data.worldbank.org/" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Data</Link>
                                <Link href="https://academy.worldbank.org/en/home   " sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Knowledge Academy</Link>
                                <Link href="https://scorecard.worldbank.org/en/scorecard/home" sx={{ color: theme.palette.common.white, display: 'block' }}>Results Scorecard</Link>
                            </Box>
                        </Box>
                    </Box>

                    {/* Stay Current Section (Row Layout) Below Links */}
                    <Box sx={{ display: 'flex', width: '100%', alignItems: 'center', justifyContent: 'space-between', marginTop: '44px' }}>
                        <Box sx={{ color: theme.palette.common.white, fontSize: isMobile ? '18px' : isTablet ? '18px' : '26px', lineHeight: '36px' }}>
                            <strong>STAY CURRENT</strong> WITH OUR LATEST DATA & INSIGHTS
                        </Box>
                        <Button
                            variant="contained"
                            sx={{
                                textTransform: 'capitalize',
                                padding: isMobile ? '8px 16px' : isTablet ? '8px 16px' : '16px 32px',
                                color: '#004370',
                                width: 'max-content',
                                borderRadius: '32px',
                                border: "2px solid rgba(0, 0, 0, 0.00)",
                                fontSize: '16px',
                                backgroundColor: theme.palette.common.white,
                                fontWeight: 700
                            }}
                            onClick={handleSignupButtonClick}
                        >
                            Sign Up
                        </Button>
                    </Box>
                </Box>

                {/* Bottom Row */}
                <Box
                    sx={{
                        display: "flex",
                        width: "100%",
                        padding: isMobile ? "16px" : isTablet ? "16px" : "44px 24.5px 72px 0px",
                        alignItems: "center",
                        justifyContent: 'space-between',
                        gap: isMobile ? "32px" : isTablet ? "32px" : "248.859px",
                        flexDirection: 'column'
                    }}
                >
                    <Typography component="label"
                        sx={{
                            fontFamily: 'HostGrotesk',
                            fontSize: '12px',
                            color: theme.palette.common.white
                        }}>
                        © 2025 World Bank Group. All rights reserved
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: '16px', justifyContent: 'center' }}>
                        <Link href="https://www.worldbank.org/en/about/legal" sx={linkStyles}>
                            Legal
                        </Link>
                        <Link href="https://www.worldbank.org/en/about/legal/privacy-notice" sx={linkStyles}>
                            Privacy Notice
                        </Link>
                        <Link href="https://www.worldbank.org/en/who-we-are/site-accessibility" sx={linkStyles}>
                            Site Accessibility
                        </Link>
                        <Link href="https://www.worldbank.org/en/access-to-information" sx={linkStyles}>
                            Access to Information
                        </Link>
                        <Link href="https://www.worldbank.org/en/about/legal/scams" sx={linkStyles}>
                            Scam Alert
                        </Link>
                        <Link href="https://wbgcmsprod.microsoftcrmportals.com/en-US/anonymous-users/int-fraud-management/create-new-complaint/" sx={linkStyles}>
                            Report Fraud or Corruption
                        </Link>
                    </Box>
                </Box>
            </Box>
        )
    };

    const renderMobileView = () => {
        return (
            <Box
                component="footer"
                sx={{
                    display: "flex",
                    padding: "24px",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "flex-start",
                    alignSelf: "stretch",
                    background: "#012740",
                }}
            >
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: '20px'
                    }}
                >
                    <Box>
                        <img
                            src="/images/header_footer/footer/wb-logo.png"
                            alt="World Bank Logo"
                            style={{ width: "250px", height: "50px" }}
                        />
                    </Box>
                    <Box sx={{ display: "flex", flexDirection: "row", gap: "8px" }}>
                        <Link
                            href="https://www.worldbank.org/en/who-we-are/ibrd"
                            sx={{
                                color: theme.palette.common.white,
                                textDecoration: "none",
                                fontSize: "16px",
                                fontWeight: "700",
                                position: "relative",
                                "&::after": {
                                    content: '""',
                                    position: "absolute",
                                    left: 0,
                                    bottom: "-0.5px",
                                    width: "100%",
                                    borderBottom: "1px solid currentColor",
                                    transform: "scaleX(0)",
                                    transformOrigin: "left",
                                },
                                "&:hover::after": {
                                    transform: "scaleX(1)",
                                },
                            }}
                        >
                            IBRD
                        </Link>
                        <Link
                            href="https://ida.worldbank.org/en/home"
                            sx={{
                                color: theme.palette.common.white,
                                textDecoration: "none",
                                fontSize: "16px",
                                fontWeight: "700",
                                position: "relative",
                                "&::after": {
                                    content: '""',
                                    position: "absolute",
                                    left: 0,
                                    bottom: "-0.5px",
                                    width: "100%",
                                    borderBottom: "1px solid currentColor",
                                    transform: "scaleX(0)",
                                    transformOrigin: "left",
                                },
                                "&:hover::after": {
                                    transform: "scaleX(1)",
                                },
                            }}
                        >
                            IDA
                        </Link>
                        <Link
                            href="https://www.ifc.org/en/home"
                            sx={{
                                color: theme.palette.common.white,
                                textDecoration: "none",
                                fontSize: "16px",
                                fontWeight: "700",
                                position: "relative",
                                "&::after": {
                                    content: '""',
                                    position: "absolute",
                                    left: 0,
                                    bottom: "-0.5px",
                                    width: "100%",
                                    borderBottom: "1px solid currentColor",
                                    transform: "scaleX(0)",
                                    transformOrigin: "left",
                                },
                                "&:hover::after": {
                                    transform: "scaleX(1)",
                                },
                            }}
                        >
                            IFC
                        </Link>
                        <Link
                            href="https://www.miga.org/"
                            sx={{
                                color: theme.palette.common.white,
                                textDecoration: "none",
                                fontSize: "16px",
                                fontWeight: "700",
                                position: "relative",
                                "&::after": {
                                    content: '""',
                                    position: "absolute",
                                    left: 0,
                                    bottom: "-0.5px",
                                    width: "100%",
                                    borderBottom: "1px solid currentColor",
                                    transform: "scaleX(0)",
                                    transformOrigin: "left",
                                },
                                "&:hover::after": {
                                    transform: "scaleX(1)",
                                },
                            }}
                        >
                            MIGA
                        </Link>
                        <Link
                            href="https://icsid.worldbank.org/"
                            sx={{
                                color: theme.palette.common.white,
                                textDecoration: "none",
                                fontSize: "16px",
                                fontWeight: "700",
                                position: "relative",
                                "&::after": {
                                    content: '""',
                                    position: "absolute",
                                    left: 0,
                                    bottom: "-0.5px",
                                    width: "100%",
                                    borderBottom: "1px solid currentColor",
                                    transform: "scaleX(0)",
                                    transformOrigin: "left",
                                },
                                "&:hover::after": {
                                    transform: "scaleX(1)",
                                },
                            }}
                        >
                            ICSID
                        </Link>
                    </Box>

                    <Box sx={{ display: "flex", gap: "8px" }}>
                        <Link href="https://www.facebook.com/worldbank" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/fb.png" alt="Facebook" width={10} height={20} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.whatsapp.com/channel/0029VaeLyAgAojYvg7Wbr91k" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/WhatsApp.png" alt="WhatsApp" width={20} height={21} />
                            </IconButton>
                        </Link>
                        <Link href="https://x.com/worldbank" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/twitter.png" alt="Twitter" width={15} height={16} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.linkedin.com/company/the-world-bank/" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/linkedln.png" alt="LinkedIn" width={16} height={16} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.instagram.com/worldbank/" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/instagram.png" alt="Instagram" width={17} height={19} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.youtube.com/user/WorldBank" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/YouTube.png" alt="YouTube" width={19} height={14} />
                            </IconButton>
                        </Link>
                        <Link href="https://www.flickr.com/photos/worldbank/" target="_blank" rel="noopener noreferrer">
                            <IconButton sx={{ ...iconStyles, background: "#004370", "&:hover": { background: "#179AF3" } }}>
                                <Image src="/images/header_footer/footer/flickr.png" alt="Flickr" width={20} height={10} />
                            </IconButton>
                        </Link>
                    </Box>
                </Box>
                {/* Center Row (Flex 1) */}
                <Box sx={{ flex: 1, width: "100%", display: "flex", flexDirection: "column", marginTop: '44px' }}>
                    {/* Links Stacked Vertically and Columns */}
                    <Box sx={{ display: 'flex', flexDirection: 'column', width: "100%" }}>
                        <Box>
                            <Link href="https://www.worldbank.org/ext/en/who-we-are" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Who We Are</Link>
                            <Link href="https://www.worldbank.org/en/news" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>News</Link>
                            <Link href="https://www.worldbank.org/en/about/careers" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Careers</Link>
                            <Link href="https://www.worldbank.org/en/about/contacts" sx={{ color: theme.palette.common.white, display: 'block' }}>Contact</Link>
                        </Box>

                        <Box marginTop={'20px'}>
                            <Link href="https://www.worldbank.org/en/where-we-work" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Countries</Link>
                            <Link href="https://www.worldbank.org/en/topic" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Topics</Link>
                            <Link href="https://projects.worldbank.org/en/projects-operations/projects-home?lang=en" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Projects & Operations</Link>
                            <Link href="https://www.worldbank.org/en/research" sx={{ color: theme.palette.common.white, display: 'block' }}>Research & Publications</Link>
                        </Box>

                        <Box marginTop={'20px'}>
                            <Link href="https://www.worldbank.org/en/events/all" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Events</Link>
                            <Link href="https://data.worldbank.org/" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Data</Link>
                            <Link href="https://academy.worldbank.org/en/home" sx={{ color: theme.palette.common.white, display: 'block', marginBottom: '8px' }}>Knowledge Academy</Link>
                            <Link href="https://scorecard.worldbank.org/en/scorecard/home" sx={{ color: theme.palette.common.white, display: 'block' }}>Results Scorecard</Link>
                        </Box>
                    </Box>

                    {/* Stay Current Section (Row Layout) Below Links */}
                    <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', alignItems: 'flex-start', justifyContent: 'flex-start', marginTop: '44px', gap: '10px' }}>
                        <Box sx={{ color: theme.palette.common.white, fontSize: isMobile ? '18px' : isTablet ? '18px' : '26px', lineHeight: '36px' }}>
                            <strong>STAY CURRENT</strong> WITH OUR LATEST DATA & INSIGHTS
                        </Box>
                        <Button variant="contained"
                            sx={{
                                textTransform: 'capitalize',
                                padding: isMobile ? '8px 16px' : isTablet ? '8px 16px' : '16px 32px',
                                color: '#004370',
                                width: 'max-content',
                                borderRadius: '32px',
                                border: "2px solid rgba(0, 0, 0, 0.00)",
                                fontSize: '16px',
                                backgroundColor: theme.palette.common.white,
                                fontWeight: 700
                            }}
                            onClick={handleSignupButtonClick}
                        >
                            Sign Up
                        </Button>
                    </Box>
                </Box>

                {/* Bottom Row */}
                <Box
                    sx={{
                        display: "flex",
                        width: "100%",
                        padding: "44px 24.5px 72px 0px",
                        alignItems: "flex-start",
                        gap: isMobile ? "32px" : isTablet ? "32px" : "248.859px",
                        flexDirection: 'column'
                    }}
                >
                    <Typography component="label"
                        sx={{
                            fontFamily: 'HostGrotesk',
                            fontSize: '12px',
                            color: theme.palette.common.white
                        }}>
                        © 2025 World Bank Group. All rights reserved
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: '5px' }}>
                        <Link href="https://www.worldbank.org/en/about/legal" sx={linkStyles}>
                            Legal
                        </Link>
                        <Link href="https://www.worldbank.org/en/about/legal/privacy-notice" sx={linkStyles}>
                            Privacy Notice
                        </Link>
                        <Link href="https://www.worldbank.org/en/who-we-are/site-accessibility" sx={linkStyles}>
                            Site Accessibility
                        </Link>
                        <Link href="https://www.worldbank.org/en/access-to-information" sx={linkStyles}>
                            Access to Information
                        </Link>
                        <Link href="https://www.worldbank.org/en/about/legal/scams" sx={linkStyles}>
                            Scam Alert
                        </Link>
                        <Link href="https://wbgcmsprod.microsoftcrmportals.com/en-US/anonymous-users/int-fraud-management/create-new-complaint/" sx={linkStyles}>
                            Report Fraud or Corruption
                        </Link>
                    </Box>
                </Box>
            </Box>
        );
    };

    return isMobile ? renderMobileView() : isTablet ? renderTabletView() : renderDesktopView();

};

export default Footer;