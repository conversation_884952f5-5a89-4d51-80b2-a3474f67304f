import React, { useState } from "react";
import { Paper, Box, Button, Grid, Typography, Card, CardContent, CardMedia, Dialog, DialogContent, IconButton, Chip } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import CloseIcon from "@mui/icons-material/Close";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";
import Image from 'next/image';

interface LinkButtonProps {
    url: string;
    iconSrc: string;
    altText: string;
    buttonText: string;
}

const LinkButton: React.FC<LinkButtonProps> = ({ url, iconSrc, altText, buttonText }) => {
    const theme = useTheme();
    return (
        <Button
            size="small"
            onClick={() => window.open(url, "_blank", "noopener,noreferrer")}
            sx={{
                display: "inline-flex",
                padding: "4px 10px",
                justifyContent: "center",
                alignItems: "center",
                borderRadius: "4px",
                background: theme.palette.action.active,
                boxShadow: "0px 1px 5px 0px rgba(0, 0, 0, 0.05), 0px 2px 2px 0px rgba(0, 0, 0, 0.05), 0px 3px 1px -2px rgba(0, 0, 0, 0.05)",
                color: theme.palette.common.white,
            }}
        >
            <Image
                src={iconSrc}
                alt={altText}
                width={18}
                height={18}
                style={{ marginRight: '4px', objectFit: "contain" }}
            />
            {buttonText}
        </Button>
    );
};

interface SocialLinksProps {
    linkedInUrl?: string;
    websiteUrl?: string;
}

const SocialLinks: React.FC<SocialLinksProps> = ({ linkedInUrl, websiteUrl }) => {
    return (
        <Box sx={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
            {linkedInUrl && (
                <LinkButton
                    url={linkedInUrl}
                    iconSrc="/images/team/LinkedIn.png"
                    altText="LinkedIn"
                    buttonText="LinkedIn"
                />
            )}
            {websiteUrl && (
                <LinkButton
                    url={websiteUrl}
                    iconSrc="/images/team/Website.png"
                    altText="Website"
                    buttonText="Website"
                />
            )}
        </Box>
    );
};

interface Profile {
    name: string;
    title: string;
    team: string;
    bio: string;
    linkedInUrl?: string;
    websiteUrl?: string,
    profilePicture?: string;
    isTeamCard?: boolean;
}

const teamProfiles: Profile[] = [
    {
        name: "Leadership",
        title: "",
        team: "Leadership",
        bio: "",
        isTeamCard: true
    },
    {
        name: "Arianna Legovini",
        title: "Director",
        team: "Leadership",
        bio: "Arianna Legovini is the Director of the Development Impact (DIME) department at the World Bank. She created the DIME model to transform the way data and impact evaluation evidence is generated and channeled into policy action. The approach increases the returns to development projects by wide margins. The model is based on an iterative process of evidence-informed adaptive change. In the DIME model, demand for data and impact evaluation research is created by putting clients first, releasing their financial and technical constraints, building capacities, and generating contextually relevant and highly influential evidence. She forged alliances with senior management and donors on knowledge priorities and case selection, raising $220M in IE research financing, to help shape the design of $22 billion in development finance. She restructured the production of impact evaluation research from piecemeal to programmatic to optimize economies of scale, introducing specialized administrative, review and data services to increase technical quality. Instituting strict procedures for research technical standards, ethics, transparency, reproducibility and data privacy and security, made DIME a global leader in research quality and credibility, through the provision of open-source tools and training for the global research community. She attracted exceptional talent to her team, aligning incentives to spur innovation, teamwork and high impact public goods, and fostering diversity and equal representation of women, and their intellectual contribution, to research. She serves as a senior adviser to a score of multilateral and bilateral agencies and collaborates with more than 300 institutions around the world.",
        linkedInUrl: "https://www.linkedin.com/in/arianna-legovini-606135/",
        websiteUrl: "",
        profilePicture: "/images/team/Arianna.png",
    },
    {
        name: "Samuel Fraiberger",
        title: "Program Lead",
        team: "Leadership",
        bio: "Sam Fraiberger is a data scientist within the Development Impact Evaluation (DIME) department where he leads DIME AI, a group dedicated to developing machine learning and AI methods and products to scale development impact. His research has appeared in leading academic journals (Science, Science Advances, PNAS, JIE) and conferences (ACL, EMNLP, KDD, ICWSM, TEDx) across disciplines, as well as in the popular press (The Wall Street Journal, The Economist, The Washington Post, Axios). He is also a visiting researcher at the NYU Center for Data Science and a fellow at MIT Connection Science.",
        linkedInUrl: "https://www.linkedin.com/in/samuelfraiberger/",
        websiteUrl: "https://www.samuelfraiberger.com/",
        profilePicture: "/images/team/Sam.png",
    },
    {
        name: "Modeling",
        title: "",
        team: "Modeling",
        bio: "",
        isTeamCard: true
    },
    {
        name: "Abelardo Carlos Martinez Lorenzo",
        title: "AI Scientist",
        team: "Modeling",
        bio: "Dr. Abelardo Carlos Martínez Lorenzo is a research scientist in Natural Language Processing (NLP) at the World Bank's Development Impact (DIME) department. He works on advancing technologies related to Information Extraction and Semantic Parsing. Abelardo holds a Bachelor's degree in Computer Science and a Master's degree in Big Data from the Erasmus Mundus program. He pursued his PhD under a Marie Curie Scholarship as part of the European Horizon 2020 project. During his research, Abelardo focused on NLP and published his findings in several relevant conferences (ACL, NAACL, LREC, Coling, and AAAI). More information is available on his website: https://carlosml26.github.io/ .",
        linkedInUrl: "https://it.linkedin.com/in/carlosml26",
        websiteUrl: "",
        profilePicture: "/images/team/Abelardo.png",
    },
    {
        name: "Arnault Gombert",
        title: "AI Engineer",
        team: "Modeling",
        bio: "Arnault Gombert, a senior NLP scientist, bridges the gap between Machine Learning research and applications, advocating for its accessibility and environmental considerations. He lectures at Barcelona School of Economics and holds degrees from ENSAE and University of Paris Saclay.",
        linkedInUrl: "https://www.linkedin.com/in/agombert/",
        websiteUrl: "",
        profilePicture: "/images/team/Arnault.png",
    },
    {
        name: "Satvik Garg",
        title: "AI Engineer",
        team: "Modeling",
        bio: "Satvik Garg is an NLP Engineer Consultant at the World Bank’s Development Impact Evaluation (DIME) department, where he focuses on efficiently extracting and interpreting causal knowledge from development economics to support the development of ImpactAI. Previously, Satvik worked as a Research Assistant in the Department of Neuroscience at the University of Rochester Medical Center (URMC), where he designed computer vision-based solutions to analyze 'reach-to-grasp' movements in stroke patients. He holds an M.Sc. in Computer Science from the University of Rochester, New York, with research interests in Knowledge Graphs, Large Language Models (LLMs), and Causal AI. More information is available on his profile: https://scholar.google.com/citations?user=Zgt1nMgAAAAJ&hl=en&oi=ao.",
        linkedInUrl: "https://www.linkedin.com/in/satvik-garg-8a7600185/",
        websiteUrl: "",
        profilePicture: "/images/team/Satvik.png",
    },
    {
        name: "Ana Areias",
        title: "AI Engineer",
        team: "Modeling",
        bio: "I’m a data scientist and LLM engineer passionate about uncovering insights from unconventional data sources. I thrive on exploring what’s “data-izable” and transforming disparate datasets into meaningful predictions that improve business processes and human well-being. Currently, I’m an LLM Engineer at the World Bank Impact AI team, working on leveraging large language models for development challenges. Previously, I worked at Kineviz, merging deep learning and graph technologies to analyze high-dimensional and interconnected data. My past experience also includes poverty prediction and big data for labor at the World Bank. I hold an MPA-ID from Harvard and have been a Fellow at Data Incubator, a DataCorps member with DataKind, and a Program Manager at Data-Pop Alliance. Beyond data, I’m an avid surfer and a struggling billiards player.",
        linkedInUrl: "http://www.linkedin.com/in/aareias",
        websiteUrl: "",
        profilePicture: "/images/team/Ana.png",
    },
    {
        name: "Piriyakorn Piriyatamwong",
        title: "AI Engineer",
        team: "Modeling",
        bio: "Piriyakorn is a machine learning engineer specializing in natural language processing (NLP) and large language models (LLMs). Her work spans the intersection of machine learning (ML) and NLP as well as information extraction and recommender systems. A former mathlete, Piriyakorn holds degrees in mathematics and statistics from University of Michigan (USA), University of Oxford (UK), and ETH Zurich (Switzerland).",
        linkedInUrl: "https://www.linkedin.com/in/piri-p/",
        websiteUrl: "",
        profilePicture: "/images/team/Piriyakorn.png",
    },
    {
        name: "Harshali Ranjan",
        title: "AI Engineer",
        team: "Modeling",
        bio: "Harshali is an AI scientist whose interests lie in applying machine learning to solve real-world problems in the developing world. She brings substantial experience to the policy space—having previously worked with the UN in Tanzania, supported the UNIDO National Representative in restructuring the cocoa value chain in Congo DRC, and assisted with a pilot implementation of Green Peatland Economy in the Indonesian peatlands of Riau and Central Kalimantan. Most recently, her experience at King’s College London used ML to estimate the effects of congenital heart disease on cortical micro and macrostructural maturation. She holds a degree from King’s College London.",
        linkedInUrl: "https://www.linkedin.com/in/harshali-ranjan-718a4a169/",
        websiteUrl: "",
        profilePicture: "/images/team/Harshali.png",
    },
    {
        name: "Riccardo Orlando",
        title: "AI Researcher",
        team: "Modeling",
        bio: "Riccardo is a researcher with a Ph.D. in Deep Learning and Natural Language Processing from Sapienza University of Rome. His research focuses on multilingual and low-resource language technologies, with a particular emphasis on Information Extraction. He has also contributed to the development of Foundation Models, including Minerva, the first family of Italian LLMs trained from scratch. Riccardo has authored papers at top NLP venues such as ACL, EMNLP, and NAACL, and continues to explore ways to make AI systems more accessible and effective for real-world applications",
        linkedInUrl: "",
        websiteUrl: "https://riccardorlando.xyz/",
        profilePicture: "/images/team/Riccardo.jpg",
    },
    {
        name: "Product",
        title: "",
        team: "Product",
        bio: "",
        isTeamCard: true
    },
    {
        name: "Madeline Bassetti",
        title: "Product Manager",
        team: "Product",
        bio: "Madeline is a Product Manager with an interest in civic technology, public goods and open source. She has applied experience in the life sciences and economic development sector. Her work within the World Bank has involved designing and digitizing complex data management systems with government stakeholders in Kenya and Jordan. Other notable work includes the development of a free and open implementation of Alpha-fold for scientific research in Australia, hosted on Galaxy Australia. She holds a Bachelor of Business (Economics) from the Queensland University of Technology.",
        linkedInUrl: "https://www.linkedin.com/in/madeline-b-0a69606a/",
        websiteUrl: "",
        profilePicture: "/images/team/Madeline.png",
    },
    {
        name: "Dhruti Sanghavi",
        title: "Product Designer",
        team: "Product",
        bio: "Dhruti is a Product Designer who seamlessly works across mediums, with a sharp focus on transforming narratives into compelling visual experiences that engage and resonate. At ImpactAI, she approaches design and strategy through a multifaceted approach, ensuring meaningful contributions to both digital and physical experiences. Among her many contributions, she has worked on notable projects with the Massachusetts Institute of Technology — Science Policy Review, Ugaoo, and Aza Fashions. Passionate about designing for impact, Dhruti strives to harness design as a force for positive change through both past and ongoing work.        ",
        linkedInUrl: "http://www.linkedin.com/in/dhruti-sanghavi",
        websiteUrl: "https://nativenarrative.in/",
        profilePicture: "/images/team/Dhruti.png",
    },
    {
        name: "Jan Willem Tulp",
        title: "Data Visualizer",
        team: "Product",
        bio: "Jan Willem Tulp is a prominent datavizioneer based in the Netherlands, known for his innovative work in data visualization. He is the founder and owner of TULP interactive, a one-man company he established in 2011, which specializes in creating custom data visualizations for a diverse range of clients including Scientific American, Nature, Philips, and UNICEF. His expertise lies in transforming complex data into engaging visual formats that effectively communicate insights to both specialized and general audiences. His work has garnered significant recognition; he has received multiple awards including silver medals at the Malofiej Awards for his infographics and was recently awarded a double gold medal at the Information is Beautiful Awards in November 2023. Tulp is also an active speaker at international conferences and has served as a judge for various visualization contests, further solidifying his reputation in the field. In addition to his professional work, Tulp’s visualizations have been exhibited globally at prestigious venues such as Ars Electronica in Austria and the Foosaner Art Museum in the USA.",
        linkedInUrl: "https://tulpinteractive.com/",
        websiteUrl: "",
        profilePicture: "/images/team/Jan.png",
    },
    {
        name: "Jackson Mrema",
        title: "Backend Developer",
        team: "Product",
        bio: "With over a decade of professional experience in technology, Jackson currently serves as a Short-term Consultant and Backend Developer in the Development Impact Evaluation (DIME) department of the World Bank. His expertise spans advanced backend development, with a focus on creating robust and scalable applications. Jackson holds a master's degree in Computer Science with a specialization in IT Security from the University of Gothenburg, Sweden. Prior to joining the World Bank, he led notable projects within reputable companies in the private sector, spanning from e-commerce to video streaming. At one notable organization, Jackson played a crucial role in developing essential features for their payment platform. He also helped streamline e-commerce logistics and implemented robust anti-counterfeit measures that saved the company millions of dollars through high-throughput processes. ",
        linkedInUrl: "https://www.linkedin.com/in/jacksonmrema/",
        websiteUrl: "",
        profilePicture: "/images/team/Jackson.png",
    },
    {
        name: "Kushnoor Pathan",
        title: "Web Development Engineer",
        team: "Product",
        bio: "With a decade of experience in web development engineering, Kushnoor brings cross-domain expertise and technology proficiency, complemented by proven management skills. Throughout her career, she’s seamlessly integrated extensive technical prowess with effective team coordination, ensuring successful project delivery. She is enthusiastic about the opportunity to leverage this background to drive impactful projects.",
        linkedInUrl: "https://www.linkedin.com/in/kushnoor/",
        websiteUrl: "https://kushnoorpatan.com",
        profilePicture: "/images/team/Kushnoor.png",
    },
    {
        name: "Grace Sekwao",
        title: "Software Engineer",
        team: "Product",
        bio: "Grace Sekwao is a results-driven software engineer with extensive expertise in frontend and backend development, building micro-services architecture, and utilizing cloud platforms like AWS, Azure & GCP to build various applications. With over eight years of experience, Grace has worked in different industries such as audio and video streaming and the automotive industry to deliver scalable solutions and user-centric interfaces. She thrives in solving complex technical problems. Grace holds a master's degree in Informatics and a bachelor's degree in Computer Science. She is also a certified AWS Solutions Architect and a recipient of the Swedish Institute Scholarship.",
        linkedInUrl: "https://www.linkedin.com/in/grace-sekwao-814a0791",
        websiteUrl: "",
        profilePicture: "/images/team/Grace.png",
    },
    {
        name: "Growth",
        title: "",
        team: "Growth",
        bio: "",
        isTeamCard: true
    },
    {
        name: "Philipp Zimmer",
        title: "Partnership Lead",
        team: "Growth",
        bio: "Philipp is a Data Scientist and Partnership Lead at DIME AI. He specializes in machine learning and natural language processing applications for research on conflicts, hate speech, misinformation, and food insecurity. Before joining the World Bank, Philipp was a Graduate Researcher in computational social science at the Massachusetts Institute of Technology (MIT). Previously, he supported the UN Executive Office of the Secretary-General in implementing the UN’s Data Strategy and establishing CRAF’d. Philipp holds an SM in Computer Science and an SM in Technology and Policy, both from MIT.",
        linkedInUrl: "https://www.linkedin.com/in/pzimmer98mit/",
        websiteUrl: "",
        profilePicture: "/images/team/Philipp.png",
    },
    {
        name: "Sharif Kazemi",
        title: "Growth & Partnership Specialist",
        team: "Growth",
        bio: "Sharif supports the growth strategy of ImpactAI through project and partnership management, leveraging his former experience as a management consultant and chief of staff. Sharif also researches the heterogeneity of AI bias in the Majority World and subsequent ramifications for equitable development. On that topic, he has led a project for the UNDP Accelerator Labs Network to design a public participation framework on AI. Sharif holds a Public Policy master’s degree from Columbia University where he was a Fulbright scholar and research assistant in machine learning. He also holds a first-class honors bachelor's degree from the London School of Economics.",
        linkedInUrl: "https://www.linkedin.com/in/sharif-kazemi/",
        websiteUrl: "",
        profilePicture: "/images/team/Sharif.png",
    },
    {
        name: "Cherry Wu",
        title: "Growth Specialist",
        team: "Growth",
        bio: "Cherry supports the growth strategy of ImpactAI through go-to-market and communications strategy and execution, drawing on her experience in private-sector tech consulting and AI research. She previously worked at the Center for Security and Emerging Technology and the Lawrence Livermore National Laboratory, where she focused on AI policy, education, and misinformation. Cherry holds a Master’s in Foreign Service from Georgetown University, where she concentrated in Science, Technology, and International Affairs, and a Bachelor's degree in Political Science from McGill University.",
        linkedInUrl: "https://www.linkedin.com/in/cherry-wu/",
        websiteUrl: "",
        profilePicture: "/images/team/Cherry_Wu.jpg",
    },
    {
        name: "Ishaan Bansal",
        title: "Growth Specialist",
        team: "Growth",
        bio: "Ishaan Bansal is an AI product and growth specialist supporting DIME AI’s efforts to build strategic partnerships and bring innovative products to market. He previously worked as a development consultant at IDinsight, supporting their government partnerships in education, health and capacity building. More recently, he has focused on scaling AI solutions for global development, first at the Agency Fund, and now at DIME AI. Ishaan is currently pursuing a dual degree with an MPA in International Development at the Harvard Kennedy School and an MBA at the Tuck School of Business at Dartmouth.",
        linkedInUrl: "https://www.linkedin.com/in/bansal-ishaan",
        websiteUrl: "",
        profilePicture: "/images/team/Bansal_Ishaan.jpg",
    },
    {
        name: "Data",
        title: "",
        team: "Data",
        bio: "",
        isTeamCard: true
    },
    {
        name: "Linxi Wang",
        title: "Development Policy Expert and Data Quality Coordinator",
        team: "Data",
        bio: "Linxi Wang serves as a Data Coordinator at the World Bank's Development Impact Evaluation (DIME) Analytics group, where she leads the annotation function of the ImpactAI Project. Her role is to ensure and maintain data quality of ground truth generation and to provide data science support. Before joining the World Bank, Linxi's research experience focused on public health topics, including cancer screening and mental health. Her experiences include study design; designing, implementing, and supervising data collection for national-level surveys; and developing cost-effective analysis models. Linxi holds an MSc in Economics from the Massachusetts Institute of Technology and a BSc in Investment Science from The Hong Kong Polytechnic University.",
        linkedInUrl: "https://www.linkedin.com/in/linxi-wang-********/",
        websiteUrl: "",
        profilePicture: "/images/team/Linxi.png",
    },
    {
        name: "Aarushi Aggarwal",
        title: "Development Policy Expert",
        team: "Data",
        bio: "Aarushi is a data scientist with the Impact AI team. She works at the intersection of innovation and social impact, focusing on how decision-makers can leverage technology to drive efficient and sustainable growth. Previously, she was a pre-doctoral researcher in corporate strategy and innovation at the Indian School of Business. Aarushi holds an MSc in Economics from the London School of Economics and Political Science and a Bachelor’s degree in Economics from SRCC, University of Delhi.",
        linkedInUrl: "https://www.linkedin.com/in/aarushi-aggarwal/",
        websiteUrl: "",
        profilePicture: "/images/team/Aarushi.png",
    },
    {
        name: "Clémence Butin",
        title: "Development Policy Expert",
        team: "Data",
        bio: "With a Master’s in Econometrics (TSE, 2021), Clémence’s interest lies in program evaluation, particularly in socioeconomic dynamics. Some of her past research has been on patterns of food consumption through econometric models, estimating the effect of income and education in Guadeloupe and mainland France. Her experience at France Assureurs sharpened her analytical skills, where she conducted surveys and studies on sustainable finance, insurer investments, and branch workforce dynamics. Additionally, her work for an education NGO in New Delhi provided invaluable perspectives, shaping her understanding of socioeconomic development dynamics.",
        linkedInUrl: "http://www.linkedin.com/in/cl%C3%A9mence-butin",
        websiteUrl: "",
        profilePicture: "/images/team/Clémence.png",
    },
    {
        name: "Ejigayehu Diriba",
        title: "Development Policy Expert",
        team: "Data",
        bio: "Ejigayehu is an econometrician with a Master of Science in Applied Economics from the University of Maryland, where she focused on topics in time series analysis, program analysis & evaluation, and development economics. Her research applies advanced statistical methods to identify opportunities for addressing key economic issues, including environmental impacts in vulnerable communities and development challenges, in diverse settings. Multilingual in Amharic, French, and Italian, her transnational background enriches her analytical perspective, supporting nuanced, data-driven insights. In her free time, she dedicates herself to making music and applying her skills to community-driven development projects.",
        linkedInUrl: "",
        websiteUrl: "https://sites.google.com/view/ejigayehugdiriba/about-me",
        profilePicture: "/images/team/Ejigayehu.png",
    },
    {
        name: "Saqib Hussain",
        title: "Development Policy Expert",
        team: "Data",
        bio: "Having engaged with development institutes including the Development Impact department (DIME) the World Bank, the Korean Development Institute (KDI), and the Pakistan Institute of Development Economics (PIDE), Saquib is interested in public policy that answers how the livelihoods of vulnerable populations can be improved. His research interests include economic development, terrorism, education, e-governance, migration, and machine learning. His research focuses on the impact evaluation of socioeconomic programs using big data. He utilizes applied econometrics and artificial intelligence models to establish causal relationships and answer important economic and research questions.",
        linkedInUrl: "https://www.linkedin.com/in/meetsaqib/",
        websiteUrl: "https://itssaqib.github.io/",
        profilePicture: "/images/team/Saqib.png",
    },
    {
        name: "Rida Ali Khan",
        title: "Development Policy Expert",
        team: "Data",
        bio: "Rida is a graduate of Public Policy at the Korean Development Institute (KDI), a distinguished think tank in South Korea. She also served as a consultant within the institute. Before joining KDI School, she completed research on the impact of the national laptop scheme on graduates' performance in the job market using regression discontinuity. With a background in public administration during her undergraduate studies and a master's degree in Management Sciences, her career endeavors are focused on research and academia in development economics. Rida’s professional interests are primarily centered on the rigorous evaluation of environmental and social programs and the analytical methodologies deployed for assessing their outcomes. ",
        linkedInUrl: "https://www.linkedin.com/in/ridaalikhan/",
        websiteUrl: "",
        profilePicture: "/images/team/Rida.png",
    },
    {
        name: "Nihaa Sajid",
        title: "Development Policy Expert",
        team: "Data",
        bio: "Nihaa is a Development Policy Expert with the ImpactAI team, focusing on systematic review annotation and curation to serve as a database for the product. During the summer of 2023, she interned with the same team and since has been interested in leveraging technology and AI for the development sector. She holds a master's in Business Analytics from George Washington University and has previously worked as an analyst for multiple startups.",
        linkedInUrl: "https://www.linkedin.com/in/nihaa-sajid1/",
        websiteUrl: "",
        profilePicture: "/images/team/Nihaa.png",
    },

];

const TeamProfile = () => {
    const theme = useTheme();
    const [selectedProfile, setSelectedProfile] = useState<Profile | null>(null);
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    return (
        <>
            <Paper sx={{
                padding: "0px", width: "100%", height: "auto", boxShadow: 0, display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center",
                gap: isMobile ? "48px" : isTablet ? "90px" : "100px",
                borderRadius: "32px", background: "none"
            }}>
                <Box sx={{ textAlign: "center", display: "flex", justifyContent: "center", flexDirection: "column", alignItems: "center", gap: 1 }}>
                    <Typography variant={isMobile ? "h1" : isTablet ? "h1" : "h1"}
                        sx={{
                            width: isMobile ? "100%" : isTablet ? "95%" : "75%",
                        }}>
                        A dedicated, diverse & multidisciplinary team drives ImpactAI.
                    </Typography>
                    <Typography variant={isMobile ? "body2" : "body1"}
                        sx={{
                            width: isMobile ? "100%" : isTablet ? "90%" : "60%",
                            textAlign: "center",
                            color: theme.palette.text.secondary,
                            fontSize: isMobile ? "12px" : "16px"
                        }}>
                        Our group comprises AI researchers, machine learning engineers, user experience designers, software developers, development economics experts, and growth specialists.                    </Typography>
                </Box>
                <Box sx={{ width: "100%" }}>
                    <Grid container spacing="21px" justifyContent="flex-start">
                        {teamProfiles.map((profile, index) => (
                            <Grid item xs={6} sm={4} md={2.4} key={index}>
                                <Card
                                    onClick={() => !profile.isTeamCard && profile.profilePicture && setSelectedProfile(profile)}
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        borderRadius: "0px",
                                        boxShadow: 0,
                                        background: profile.isTeamCard ? theme.palette.secondary.light : "#F5FAFF",
                                        cursor: !profile.isTeamCard && profile.profilePicture ? "pointer" : "default",
                                        width: "100%",
                                        height: "306px",
                                        position: "relative",
                                        overflow: "hidden",
                                    }}
                                >
                                    {profile.isTeamCard ? (
                                        <CardContent sx={{ padding: "10px", textAlign: "center", height: "100%", display: "flex", alignItems: "flex-end", justifyContent: "flex-start", color: theme.palette.common.white }}>
                                            <Typography variant="h1" sx={{ color: `${theme.palette.primary.main} !important` }}>{profile.name}</Typography>
                                        </CardContent>
                                    ) : (
                                        <>
                                            {profile.profilePicture && (
                                                <CardMedia
                                                    component="img"
                                                    image={profile.profilePicture}
                                                    alt={profile.name}
                                                    sx={{
                                                        width: "100%",
                                                        height: "100%",
                                                        objectFit: "cover"
                                                    }}
                                                />
                                            )}
                                            <CardContent
                                                sx={{
                                                    position: "absolute",
                                                    bottom: 0,
                                                    left: 0,
                                                    right: 0,
                                                    color: "white",
                                                    padding: "10px !important",
                                                }}>
                                                <Chip label={profile.team} sx={{ height: "30px", padding: "10px", borderRadius: "32px", background: "#E8F0FC", color: `${theme.palette.primary.main} !important` }} />
                                                <Box sx={{ height: '21px' }} />
                                                <Typography variant="subtitle1" sx={{ fontWeight: "bold", fontSize: "0.9rem", color: `${theme.palette.common.white} !important` }}>{profile.name}</Typography>
                                                <Typography variant="body2" sx={{ fontSize: "0.8rem", color: `${theme.palette.common.white} !important` }}>{profile.title}</Typography>
                                            </CardContent>
                                        </>
                                    )}
                                </Card>
                            </Grid>
                        ))}
                    </Grid>
                </Box>
            </Paper>
            {selectedProfile && (
                <Dialog
                    open={Boolean(selectedProfile)}
                    onClose={() => setSelectedProfile(null)}
                    fullWidth
                    maxWidth="md"
                    slotProps={{
                        backdrop: {
                            sx: {
                                backgroundColor: "rgba(0, 0, 0, 0.5)",
                                backdropFilter: "blur(10px)",
                            },
                        },
                    }}
                    PaperProps={{
                        style: {
                            borderRadius: '16px',
                        },
                    }}
                >
                    <DialogContent sx={{ p: "30px", backgroundColor: theme.palette.background.default }}>
                        {/* Mobile View */}
                        {isMobile && (
                            <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
                                <Box sx={{ width: '100%', position: "relative" }}>
                                    <CardMedia
                                        component="img"
                                        image={selectedProfile.profilePicture}
                                        alt={selectedProfile.name}
                                        sx={{
                                            width: '100%',
                                            height: 'auto',
                                            objectFit: 'cover',
                                        }}
                                    />
                                    <Box
                                        sx={{
                                            position: "absolute",
                                            top: "10px",
                                            left: "10px",
                                            right: "10px",
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                            alignItems: 'flex-start',
                                            width: 'calc(100% - 20px)'
                                        }}
                                    >
                                        <Chip
                                            label={selectedProfile.team}
                                            sx={{
                                                height: "30px",
                                                padding: "10px",
                                                borderRadius: "32px",
                                                background: "#E8F0FC",
                                                color: `${theme.palette.primary.main} !important`
                                            }}
                                        />
                                        <IconButton
                                            onClick={() => setSelectedProfile(null)}
                                            sx={{
                                                display: 'flex',
                                                height: '30px',
                                                width: '30px',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                backgroundColor: theme.palette.action.active,
                                                color: 'white',
                                                '&:hover': {
                                                    backgroundColor: '#003380',
                                                },
                                            }}
                                        >
                                            <CloseIcon />
                                        </IconButton>
                                    </Box>
                                </Box>
                                {/* Mobile Profile Details */}
                                <Box sx={{ my: "21px" }}>
                                    <Typography variant="body2" sx={{ fontWeight: 'bold', color: `${theme.palette.primary.main} !important` }}>{selectedProfile.name}</Typography>
                                    <Typography variant="body2">{selectedProfile.title}</Typography>
                                    <Typography variant="body2" sx={{ opacity: 0.7, mt: "10px", color: theme.palette.text.primary }}>{selectedProfile.bio}</Typography>
                                </Box>
                                {/* Mobile Social Links */}
                                <SocialLinks linkedInUrl={selectedProfile.linkedInUrl} websiteUrl={selectedProfile.websiteUrl} />
                            </Box>
                        )}

                        {isTablet && (
                            <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
                                <Box sx={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', alignSelf: 'stretch' }}>
                                    <Box sx={{ width: '60%', position: "relative" }}>
                                        <CardMedia
                                            component="img"
                                            image={selectedProfile.profilePicture}
                                            alt={selectedProfile.name}
                                            sx={{
                                                width: '100%',
                                                height: 'auto',
                                                objectFit: 'cover',
                                            }}
                                        />
                                    </Box>
                                    <IconButton
                                        onClick={() => setSelectedProfile(null)}
                                        sx={{
                                            display: 'flex',
                                            height: '30px',
                                            width: '30px',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            backgroundColor: theme.palette.action.active,
                                            color: 'white',
                                            '&:hover': {
                                                backgroundColor: '#003380',
                                            },
                                        }}
                                    >
                                        <CloseIcon />
                                    </IconButton>
                                </Box>
                                <Box sx={{ width: '60%', mt: '10px' }}>
                                    <Chip
                                        label={selectedProfile.team}
                                        sx={{
                                            height: "30px",
                                            padding: "10px",
                                            borderRadius: "32px",
                                            background: "#E8F0FC",
                                            color: `${theme.palette.primary.main} !important`
                                        }}
                                    />
                                </Box>
                                {/* Tablet Profile Details below image */}
                                <Box sx={{ my: "21px" }}>
                                    <Typography variant="body2" sx={{ fontWeight: 'bold', color: `${theme.palette.primary.main} !important` }}>{selectedProfile.name}</Typography>
                                    <Typography variant="body2">{selectedProfile.title}</Typography>
                                    <Typography variant="body2" sx={{ opacity: 0.7, mt: "10px", color: theme.palette.text.primary }}>{selectedProfile.bio}</Typography>
                                </Box>
                                {/* Tablet Social Links */}
                                <SocialLinks linkedInUrl={selectedProfile.linkedInUrl} websiteUrl={selectedProfile.websiteUrl} />
                            </Box>
                        )}
                        {/* Desktop View */}
                        {!(isMobile || isTablet) && (
                            <Box sx={{ display: 'flex', gap: 2 }}>
                                <CardMedia
                                    component="img"
                                    image={selectedProfile.profilePicture}
                                    alt={selectedProfile.name}
                                    sx={{
                                        width: '256px',
                                        height: 'auto',
                                        flexShrink: 0,
                                        objectFit: 'cover',
                                    }}
                                />
                                <Box sx={{ flex: 1 }}>
                                    {/* Desktop Chip and Close Button */}
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: '21px' }}>
                                        <Chip label={selectedProfile.team} sx={{ borderRadius: '100px', background: '#E8F0FC', color: `${theme.palette.primary.main} !important` }} />
                                        <IconButton onClick={() => setSelectedProfile(null)} sx={{ display: 'flex', height: '30px', width: '30px', justifyContent: 'center', alignItems: 'center', backgroundColor: theme.palette.action.active, color: 'white', '&:hover': { backgroundColor: '#003380' } }}>
                                            <CloseIcon />
                                        </IconButton>
                                    </Box>
                                    {/* Desktop Profile Details */}
                                    <Box sx={{ my: "21px" }}>
                                        <Typography variant="body2" sx={{ fontWeight: 'bold', color: `${theme.palette.primary.main} !important` }}>{selectedProfile.name}</Typography>
                                        <Typography variant="body2">{selectedProfile.title}</Typography>
                                        <Typography variant="body2" sx={{ opacity: 0.7, mt: "10px", color: theme.palette.text.primary }}>{selectedProfile.bio}</Typography>
                                    </Box>
                                    {/* Desktop Social Links */}
                                    <SocialLinks linkedInUrl={selectedProfile.linkedInUrl} websiteUrl={selectedProfile.websiteUrl} />
                                </Box>
                            </Box>
                        )}
                    </DialogContent>
                </Dialog>
            )}

        </>
    );
};

export default TeamProfile;