"""Impact pipeline for causal impact queries."""

from typing import Dict, Any, List
from src.pipelines.base import Pipeline
from src.tools.manager import ToolManager
from src.pipelines.error_handling import PipelineErrorHandler
from src.pipelines.outputs import create_pipeline_response
import logging

logger = logging.getLogger(__name__)


class ImpactPipeline(Pipeline):
    """Pipeline for causal impact queries exploring whether and how X affects Y."""

    def __init__(self, tool_manager: ToolManager, config: Dict[str, Any] | None = None):
        """Initialize the impact pipeline."""
        super().__init__(
            name="impact_pipeline",
            description="Pipeline for causal impact queries exploring treatment effects and statistically significant changes due to interventions",
            intent="causal impact",
            steps=[
                "entity_extractor",
                "sql_generator",
                "structured_data_organizer",
                "rag_search",
                "final_answer_generator",
            ],
            arguments=[("user_query", "str")],
            outputs="FinalAnswer(text: str, sources: List[str], metadata: Dict[str, Any])",
            config=config,
        )
        self.tool_manager = tool_manager
        self.error_handler = PipelineErrorHandler(
            verbose=self.config.get("verbose", False)
        )

    def get_steps(self) -> List[str]:
        """Get the steps for impact analysis."""
        return self.steps

    async def execute(self, user_query: str, **kwargs) -> Dict[str, Any]:
        """Execute the impact pipeline."""
        try:
            if self.verbose:
                logger.info(f"Starting impact pipeline for query: {user_query}")

            # Step 1: Entity Extraction
            entities = await self._execute_step(
                "entity_extractor", user_query=user_query
            )
            if not self.error_handler.has_meaningful_entities(entities):
                return self.error_handler.handle_no_entities(
                    "causal impact", user_query, self.name
                )

            # Step 2: SQL Generation and Execution
            query_result = await self._execute_step("sql_generator", entities=entities)

            # Step 3: Structured Data Organization only if data is found
            structured_data = None
            if query_result.row_count != 0:
                structured_data = await self._execute_step(
                    "structured_data_organizer",
                    user_query=user_query,
                    entities=entities,
                    dataset=query_result.dataset,
                )

            # Step 4: RAG Search for implementation information
            # rag_results = await self._execute_step(
            #     "rag_search", query_result=user_query, num_results=15
            # )

            # Step 5: Final Answer Generation
            final_answer_kwargs = {}
            # final_answer_kwargs = {"rag_results": rag_results}
            if structured_data:
                final_answer_kwargs["structured_data"] = structured_data
            final_answer = await self._execute_step(
                "final_answer_generator",
                user_query=user_query,
                **final_answer_kwargs,
            )

            return create_pipeline_response(
                intent="causal impact",
                status="completed_successfully",
                user_query=user_query,
                observation=final_answer.text,
                answer=final_answer.text,
                pipeline_name=self.name,
                thought=f"Successfully executed impact pipeline with {query_result.row_count} data points from {query_result.unique_papers} papers. The analysis provides comprehensive causal impact information.",
                steps_completed=self.get_steps(),
                data_points=query_result.row_count,
                papers_analyzed=query_result.unique_papers,
            )

        except Exception as e:
            return self.error_handler.handle_pipeline_error(
                "causal impact", e, user_query, self.name
            )

    async def _execute_step(self, step_name: str, **kwargs):
        """Execute a pipeline step with error handling and tracking."""
        try:
            self.current_step += 1
            if self.verbose:
                logger.info(f"Step {self.current_step}: Executing {step_name}")

            # Start tracking the step
            self._track_step_start(step_name, kwargs)

            result = await self.tool_manager.execute_with_cache(step_name, **kwargs)
            self.results[step_name] = result

            # Track successful completion
            output_summary = {"type": type(result).__name__}
            if hasattr(result, "row_count"):
                output_summary["row_count"] = result.row_count
            if hasattr(result, "text"):
                output_summary["text_length"] = len(result.text)

            self._track_step_end(success=True, output_data=output_summary)

            return result

        except Exception as e:
            if self.verbose:
                logger.error(f"Error in {step_name}: {e}")

            # Track the error
            self._track_step_end(success=False, error_message=str(e))
            raise
