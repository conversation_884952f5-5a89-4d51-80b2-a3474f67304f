import pytest
from unittest.mock import Mock, patch, AsyncMock
from services.search import SearchService


@pytest.fixture
def search_service():
    with patch("services.search.FilesService") as mock_files:
        service = SearchService()
        service.files_service = mock_files
        yield service


@pytest.fixture
def mock_session():
    session = AsyncMock()
    # Set up the async context manager protocol
    session.__aenter__ = AsyncMock(return_value=session)
    session.__aexit__ = AsyncMock(return_value=None)
    # Mock the execute method to return a proper result
    mock_result = AsyncMock()
    session.execute = AsyncMock(return_value=mock_result)
    return session


@pytest.mark.asyncio
async def test_initialize_entries(search_service):
    # Mock the private method
    with patch.object(
        search_service, "_SearchService__fetch_top_tags_by_type"
    ) as mock_fetch:
        mock_fetch.side_effect = [
            [{"tag": "intervention1"}],  # interventions
            [{"tag": "outcome1"}],  # outcomes
        ]

        # First initialization
        assert not search_service.initialized
        await search_service.initialize_entries()
        assert search_service.initialized
        assert search_service.intervention_tags == [{"tag": "intervention1"}]
        assert search_service.outcome_tags == [{"tag": "outcome1"}]
        assert mock_fetch.call_count == 2

        # Second initialization should not fetch again
        await search_service.initialize_entries()
        assert mock_fetch.call_count == 2  # Still 2, not 4


@pytest.mark.asyncio
async def test_get_chips(search_service):
    mock_response = {"type1": "value1", "type2": "value2"}

    with patch("services.search.post_json", return_value=mock_response):
        chips = await search_service.get_chips()

        assert len(chips) == 2
        assert chips[0] == {"type": "type1", "value": "value1", "label": "value1"}
        assert chips[1] == {"type": "type2", "value": "value2", "label": "value2"}


def test_get_entries_interventions(search_service):
    search_service.intervention_tags = [{"tag": f"intervention{i}"} for i in range(10)]

    entries = search_service.get_entries("interventions")
    assert len(entries) == search_service.items_per_page
    assert all(entry in search_service.intervention_tags for entry in entries)


def test_get_entries_outcomes(search_service):
    search_service.outcome_tags = [{"tag": f"outcome{i}"} for i in range(10)]

    entries = search_service.get_entries("outcomes")
    assert len(entries) == search_service.items_per_page
    assert all(entry in search_service.outcome_tags for entry in entries)


def test_get_entries_empty_lists(search_service):
    assert search_service.get_entries("interventions") == []
    assert search_service.get_entries("outcomes") == []


@pytest.mark.asyncio
async def test_fetch_top_tags_by_type(search_service, mock_session):
    # Mock the database session context manager
    mock_get_session = Mock(return_value=mock_session)
    with patch("services.search.get_core_db_session", mock_get_session):
        # Mock the load_tags_request method
        search_service.files_service.load_tags_request.return_value = (
            "SELECT * FROM tags WHERE type = :tag_type"
        )

        # Mock the session execute result
        mock_result = Mock()
        mock_result.all.return_value = [
            Mock(_asdict=lambda: {"tag": "tag1"}),
            Mock(_asdict=lambda: {"tag": "tag2"}),
        ]
        mock_session.execute.return_value = mock_result

        # Test fetching intervention tags
        result = await search_service._SearchService__fetch_top_tags_by_type(
            "intervention"
        )
        assert result == [{"tag": "tag1"}, {"tag": "tag2"}]

        # Verify SQL query was properly formatted
        mock_session.execute.assert_called_once()

        # Reset mock for next test
        mock_session.execute.reset_mock()

        # Test fetching outcome tags
        result = await search_service._SearchService__fetch_top_tags_by_type("outcomes")
        assert result == [{"tag": "tag1"}, {"tag": "tag2"}]
        mock_session.execute.assert_called_once()
