from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
import os
from typing import AsyncGenerator, Optional
import logging
from contextlib import asynccontextmanager


logger = logging.getLogger(__name__)

db_host = os.getenv("MYSQL_HOST")
db_name = os.getenv("MYSQL_DATABASE")
db_user = os.getenv("MYSQL_USER")
db_password = os.getenv("MYSQL_PASSWORD")

use_sql_proxy = os.getenv("USE_SQL_PROXY", "false").lower() == "true"
cloud_sql_instance = os.getenv(
    "CLOUD_SQL_INSTANCE", "impactai-430615:us-east1:impactai-db"
)

DATABASE_URL = f"mysql+aiomysql://{db_user}:{db_password}@{db_host}/{db_name}"
if use_sql_proxy:
    unix_socket = f"/cloudsql/{cloud_sql_instance}"
    DATABASE_URL = (
        f"mysql+aiomysql://{db_user}:{db_password}@/{db_name}?unix_socket={unix_socket}"
    )

engine = create_async_engine(
    DATABASE_URL,
    echo=False,
    pool_size=20,
    max_overflow=20,
    pool_timeout=30,
    pool_recycle=1800,
    pool_pre_ping=True,
    execution_options={"compiled_cache": {}},
    connect_args={"connect_timeout": 10},
)

AsyncSessionLocal = async_sessionmaker(
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False,
)


@asynccontextmanager
async def get_userdata_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Context manager for database sessions with improved error handling."""
    session: Optional[AsyncSession] = None
    try:
        session = AsyncSessionLocal()
        yield session
    except Exception as e:
        logger.error(f"Session error: {e}")
        if session:
            await session.rollback()
        raise
    finally:
        if session:
            await session.close()
            logger.debug("Session closed, connection returned to pool")
