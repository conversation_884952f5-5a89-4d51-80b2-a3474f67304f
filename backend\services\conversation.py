from typing import Any, Dict, List
import uuid
from datetime import datetime
from sqlalchemy import select, update, insert
from sqlalchemy.orm import Session

from database.models import Conversation, Feedback, Message, Plot, Source
from database.userdata import get_userdata_db_session
from utils.measure import measure_async_time
from services.agent import get_gemini_completion
from utils.filesystem import get_prompt_file_path


async def clear_user_conversations(user_id: uuid.UUID):
    """
    Deletes all conversations for a specific user.

    """
    async with get_userdata_db_session() as session:
        update_query = (
            update(Conversation)
            .where(Conversation.user_id == str(user_id))
            .values(deleted_at=datetime.now())
        )
        await session.execute(update_query)
        await session.commit()


@measure_async_time
async def fetch_conversations(user_id: uuid.UUID) -> List[Conversation]:
    """
    Fetches all conversations for a specific user.

    Returns:
        List[Conversation]: A list of conversations associated with the user.
    """
    async with get_userdata_db_session() as session:
        query = (
            select(Conversation)
            .where(Conversation.user_id == str(user_id))
            .where(Conversation.deleted_at.is_(None))
            .order_by(Conversation.created_at.desc())
        )
        result = await session.execute(query)
        return result.scalars().all()


@measure_async_time
async def initialise_new_conversation(
    user_id: uuid.UUID,
    conversation_id: uuid.UUID,
):
    """
    Initializes the conversation if it doesn't already exist.

    Args:
        user_id (uuid.UUID): The ID of the user
        conversation_id (uuid.UUID): The ID of the conversation

    Returns:
        dict: Contains user_id and conversation_id.
    """
    async with get_userdata_db_session() as session:
        stmt = (
            insert(Conversation)
            .values(id=conversation_id, user_id=user_id)
            .prefix_with("IGNORE")
        )
        await session.execute(stmt)
        await session.commit()


@measure_async_time
async def update_conversation_title(conversation_id: uuid.UUID, text: str):
    async with get_userdata_db_session() as session:
        update_stmt = (
            update(Conversation)
            .where(Conversation.id == str(conversation_id))
            .where(Conversation.title.is_(None))
            .values(title=text)
        )
        await session.execute(update_stmt)
        await session.commit()


@measure_async_time
async def insert_conversation_message(
    conversation_id: uuid.UUID,
    text: str = "",
    author: str = "user",
    type: str = "answer",
):
    async with get_userdata_db_session() as session:
        message_id = str(uuid.uuid4())
        stmt = insert(Message).values(
            id=message_id,
            conversation_id=str(conversation_id),
            author=author,
            type=type,
            text=text,
        )
        await session.execute(stmt)
        await session.commit()

        return {"message_id": message_id}


@measure_async_time
async def initialize_information_message(
    conversation_id: uuid.UUID,
    query_values: Dict[str, Any],
):
    async with get_userdata_db_session() as session:
        message_id = str(uuid.uuid4())
        stmt = insert(Message).values(
            id=message_id,
            conversation_id=str(conversation_id),
            author="system",
            type="information",
            text=None,
            choices=None,
            summary_configs=None,
            query_values=query_values,
        )
        await session.execute(stmt)
        await session.commit()

        return {"message_id": message_id}


@measure_async_time
async def fetch_messages_by_conversation_id(conversation_id: uuid.UUID):
    async with get_userdata_db_session() as session:
        query = (
            select(Message)
            .where(Message.conversation_id == str(conversation_id))
            .order_by(Message.created_at.asc(), Message.updated_at.asc())
        )
        result = await session.execute(query)
        return result.scalars().all()


@measure_async_time
async def fetch_plots_by_message_ids(message_ids: List[uuid.UUID]):
    async with get_userdata_db_session() as session:
        query = select(Plot).where(Plot.message_id.in_(message_ids))
        result = await session.execute(query)
        return result.scalars().all()


@measure_async_time
async def fetch_sources_by_message_ids(message_ids: List[uuid.UUID]):
    async with get_userdata_db_session() as session:
        query = (
            select(Source)
            .where(Source.message_id.in_(message_ids))
            .order_by(Source.position.asc())
        )
        result = await session.execute(query)
        return result.scalars().all()


@measure_async_time
async def fetch_conversation_messages(conversation_id: uuid.UUID):
    """
    Fetches all messages in the current conversation.

    Returns:
        List: A list of message data including related sources and plots.
    """
    async with get_userdata_db_session() as session:
        query = (
            select(
                Message.id,
                Message.author,
                Message.text,
                Message.type,
                Message.created_at,
                Message.updated_at,
                Source.id.label("source_id"),
                Source.title.label("source_title"),
                Source.paper_id.label("source_paper_id"),
                Source.short_paper_id.label("source_short_paper_id"),
                Source.doi_url.label("source_doi_url"),
                Source.citation.label("source_citation"),
                Plot.id.label("plot_id"),
                Plot.title.label("plot_title"),
                Plot.data.label("plot_data"),
            )
            .join(Source, Source.message_id == Message.id, isouter=True)
            .join(Plot, Plot.message_id == Message.id, isouter=True)
            .where(Message.conversation_id == str(conversation_id))
            .order_by(Message.created_at.asc(), Message.updated_at.asc())
        )
        result = await session.execute(query)
        return result.mappings().all()


@measure_async_time
async def fetch_downloadable_plot_data(message_id: uuid.UUID):
    """
    Fetches the plot data associated with the current message.

    Returns:
        List[Dict]: The flattened plot data if found, otherwise an empty list.
    """
    async with get_userdata_db_session() as session:
        result = await session.execute(
            select(Plot).where(Plot.message_id == str(message_id))
        )
        plot_data = result.scalars().first()

        if not plot_data:
            return []

        flattened_plot_data = []
        for plot_data_row in plot_data.data:
            aggregate = plot_data_row["aggregate"]
            for point in plot_data_row["data"]:
                scores = point["score"]
                flattened_plot_data.append(
                    {
                        "outcome": plot_data_row["outcome"],
                        "intervention": plot_data_row["intervention"],
                        "aggregate_lower": aggregate["lower"],
                        "aggregate_upper": aggregate["upper"],
                        "aggregate_value": aggregate["value"],
                        "paper_title": point["paper_title"],
                        "paper_citation": point["paper_citation"],
                        "paper_score_lower_bound": scores["lower"],
                        "paper_score_upper_bound": scores["upper"],
                        "paper_score_value_bound": scores["value"],
                    }
                )

        return flattened_plot_data


@measure_async_time
async def fetch_message(message_id: uuid.UUID) -> Message | None:
    """
    Fetches the summary of the current message.

    Returns:
        Message or None: The message object if found, otherwise None.
    """
    async with get_userdata_db_session() as session:
        result = await session.execute(
            select(Message).where(Message.id == str(message_id))
        )
        return result.scalars().first()


@measure_async_time
async def add_message_feedback(
    message_id: uuid.UUID,
    reaction: str,
    message: str,
    tags: str,
):
    """
    Adds feedback for a specific message.

    Args:
        reaction (str): The feedback reaction.
        message (str): The feedback message.
        tags (str): The feedback tags.

    Returns:
        dict: Contains the feedback_id of the added feedback.
    """
    async with get_userdata_db_session() as session:
        feedback_id = str(uuid.uuid4())
        stmt = insert(Feedback).values(
            id=feedback_id,
            message_id=str(message_id),
            reaction=reaction,
            message=message,
            tags=tags,
        )
        await session.execute(stmt)
        await session.commit()
        return {"feedback_id": feedback_id}


@measure_async_time
async def add_message_sources(
    message_id: uuid.UUID,
    sources: List[Dict[str, Any]],
):
    """
    Adds sources associated with a message.

    Args:
        sources (List[Dict[str, Any]]): The sources to be added.

    Returns:
        List[Source]: The sources that were added.
    """
    async with get_userdata_db_session() as session:
        source_objects = []
        for idx, source_data in enumerate(sources):
            source_dict = dict(source_data)
            source_dict["message_id"] = str(message_id)
            source_id = str(uuid.uuid4())
            source_dict["id"] = source_id
            source_dict["position"] = idx + 1
            stmt = insert(Source).values(**source_dict)
            await session.execute(stmt)
            source_objects.append(source_dict)

        await session.commit()
        return source_objects


@measure_async_time
async def add_message_plot(
    message_id: uuid.UUID,
    title: str,
    data: Any,
):
    """
    Adds a plot associated with a message.

    Args:
        title (str): The title of the plot.
        data (Any): The data for the plot.

    Returns:
        dict: Contains the plot_id of the added plot.
    """
    async with get_userdata_db_session() as session:
        plot_id = str(uuid.uuid4())
        stmt = insert(Plot).values(
            id=plot_id, message_id=str(message_id), title=title, data=data
        )
        await session.execute(stmt)
        await session.commit()
        return {"plot_id": plot_id}


@measure_async_time
async def update_message(
    message_id: uuid.UUID,
    updated_data: Dict[str, Any],
) -> None:
    """
    Updates a message with the provided data.

    Args:
        updated_data (Dict[str, Any]): The data to update the message with.

    Raises:
        HTTPException: If a database error occurs during the update.
    """
    async with get_userdata_db_session() as session:

        update_stmt = (
            update(Message).where(Message.id == str(message_id)).values(**updated_data)
        )
        await session.execute(update_stmt)
        await session.commit()


@measure_async_time
async def fetch_message_plot(
    message_id: uuid.UUID,
) -> Plot | None:
    """
    Fetches the plot associated with the current message.

    Returns:
        Plot: The plot object if found, otherwise None.
    """
    async with get_userdata_db_session() as session:
        select_stmt = select(Plot).where(Plot.message_id == str(message_id))
        result = await session.execute(select_stmt)
        return result.scalars().first()


@measure_async_time
async def fetch_message_sources(
    message_id: uuid.UUID,
) -> List[Source]:
    """
    Fetches sources associated with the current message.

    Returns:
        List[Source]: A list of sources associated with the message.
    """
    async with get_userdata_db_session() as session:
        select_stmt = (
            select(Source)
            .where(Source.message_id == str(message_id))
            .order_by(Source.created_at.asc(), Source.updated_at.asc())
        )
        result = await session.execute(select_stmt)
        return result.scalars().all()


async def gen_and_update_conversation_title(conversation_id: uuid.UUID, text: str):
    """
    Generates a title for a conversation using Gemini and updates the database.
    If the generation fails, it falls back to the original text.
    """
    try:
        file_path = get_prompt_file_path("summarize_query_title.txt")
        if not file_path.exists():
            print(f"Error: Prompt file not found at {file_path}")
            await update_conversation_title(conversation_id, text)
            return

        with open(file_path, "r") as f:
            prompt_template = f.read()

        prompt = prompt_template.format(query=text)
        generated_title = await get_gemini_completion(prompt)

        # Clean and validate the generated title
        final_title = generated_title.strip().strip("'\"")

        # Ensure the title is not empty and has a reasonable length before updating
        if final_title and 2 < len(final_title) <= 150:
            await update_conversation_title(conversation_id, final_title)
        else:
            # Fallback to the original text if the generated title is invalid
            await update_conversation_title(conversation_id, text)

    except Exception as e:
        print(f"Error generating title for conversation {conversation_id}: {e}")
        # Fallback to the original text in case of any error during generation
        await update_conversation_title(conversation_id, text)
