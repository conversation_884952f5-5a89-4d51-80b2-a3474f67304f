import React, { useState, useRef, useEffect } from "react";
import { Paper, Box, Typography, Accordion, AccordionSummary, AccordionDetails, List, ListItem, ListItemText, Link } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";

interface FaqInfoProps {
    chipId: string | null;
}

const FaqInfo: React.FC<FaqInfoProps> = ({ chipId }) => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const [expandedAccordion, setExpandedAccordion] = useState<string | null>(null);
    const [scrollToAccordion, setScrollToAccordion] = useState<string | null>(null);
    const accordionRefs = useRef<{ [key: string]: HTMLElement | null }>({});
    const faqList = [
        {
            id: "general",
            headerTitle: "General Information",
            list1Info: [
                {
                    question: "How does the site work?",
                    information: "ImpactAI uses customized large language models (LLMs) and is powered by a structured knowledge database of validated research studies. It retrieves relevant information from these studies, provides summaries of research findings, visualizes the impact of different policy options listed in the summaries, provides links to the research sources, and recommends follow-up prompts to support further exploration. "
                },
                {
                    question: "How is ImpactAI different from generic generative AI tools?",
                    information: "ImpactAI’s differentiating factor is our curated knowledge database of research studies. By feeding quantitative, structured information on policy interventions, outcomes, standardized effect sizes, and study design, ImpactAI significantly reduces the risk of hallucinations that generic generative AI tools exhibit."
                },
                {
                    question: "Who is ImpactAI designed for?",
                    information: "ImpactAI is built for development practitioners, policymakers, researchers, international organizations, and other stakeholders who need accurate, research-backed insights to inform their policy decisions."
                },
                {
                    question: "How does ImpactAI help improve development interventions?",
                    information: "ImpactAI allows users to quickly access and compare the impact of different policy interventions. For example, if you are working to reduce food insecurity, it provides summaries of studies that show the effectiveness of various approaches, helping you allocate resources more effectively."
                }
            ]
        },
        {
            id: "access",
            headerTitle: "Availability & Access ",
            list1Info: [
                {
                    question: "When will ImpactAI be available for use?",
                    information: "ImpactAI is expected to be publicly available in Spring 2025."
                },
                {
                    question: "Who will be able to use ImpactAI?",
                    information: "ImpactAI will be open-source and free to access, available to all users, including private firms and nonprofits. You can sign up for updates on the product launch here."
                },
                {
                    question: "Will ImpactAI include papers that are subscription based?",
                    information: "Yes, ImpactAI will include papers that are subscription-based, in addition to open-access studies. Please note that some sources may require a login or membership to view in full."
                }
            ]
        },
        {
            id: "research",
            headerTitle: "Research & Data Sources",
            list1Info: [
                {
                    question: "Does ImpactAI only provide information from top, published journals?",
                    information: "No, ImpactAI includes research beyond top academic journals. ImpactAI also aims to cover a wide range of research, not limited to top journals, though the quality of included research is prioritized."
                },
                {
                    question: "Why does ImpactAI primarily focus on randomized controlled trials (RCTs)?",
                    information: "The initial phase of ImpactAI prioritizes randomized controlled trials (RCTs) because they are often considered the gold standard in evaluating interventions. However, future versions will include other forms of peer-reviewed program evaluation, including natural experiments and quasi-experimental designs."
                },
                {
                    question: "Does ImpactAI cover literature outside of development economics?",
                    information: "Currently, ImpactAI covers only development economics research. However, the tool is designed to evolve, and future versions may include research from other disciplines."
                },
                {
                    question: "Does ImpactAI incorporate a quality assessment of the research?",
                    information: "Yes, our team tags and validates the quality of each paper to ensure it meets our rigorous inclusion criteria. We incorporate quality indicators like citation counts, publication in peer-reviewed journals or reputable working paper series, and the robustness of the research’s methodology to ensure high-quality insights."
                },
                {
                    question: "Does ImpactAI create the taxonomy of interventions and outcomes by itself, or is it derived from existing taxonomies?",
                    information: "ImpactAI's taxonomies are derived from various existing, established taxonomies to ensure accuracy and standardization to users."
                },
                {
                    question: "How does ImpactAI account for potentially poor language and narration from research reports and studies?",
                    information: "Poor language in research studies could impact how information is extracted and populated in our database. Although diverse data may affect overall performance, our current model is already robust. We are developing specific solutions to address challenging cases."
                },
                {
                    question: "Can private firms add their research or evaluations to the repository?",
                    information: "Yes, we are open to new research sources. All research must be peer-reviewed before submission for consideration and will be subject to our terms and conditions. Submission does not guarantee inclusion in the database."
                }
            ]
        },
        {
            id: "techfeatures",
            headerTitle: "Technology & Features",
            list1Info: [
                {
                    question: "Can I directly access the sources from the summaries provided by ImpactAI?",
                    information: "Yes, users are provided with links to access the original research sources. Some sources may require login or membership with the publisher or data partner."
                },
                {
                    question: "Can I visualize research findings with ImpactAI?",
                    information: "Yes, ImpactAI provides interactive graphs that visualize the impact of different policy interventions and directly link to the data points' respective sources."
                },
                {
                    question: "Is ImpactAI related to EconBERTa?",
                    information: "Yes, our team developed EconBERTa, which informed the development of ImpactAI."
                },
                {
                    question: "How are ImpactAI's language models trained to give reliable results?",
                    information: "The language models are fine-tuned on a structured knowledge base that includes validated research studies. Human annotators ensure that the model is trained on relevant, high-quality data, and we use our own curated validation datasets to measure our models’ performance."
                },
                {
                    question: "How does ImpactAI account for potential biases?",
                    information: "Biases primarily affect summarization tasks. To mitigate this, we restrict the model's access to our validated database, impose rigorous guidelines, and implement structured prompting frameworks. These measures help reduce hallucinations and limit the spread of bias. All outputs are reviewed by both LLM-based evaluators and human reviewers to maintain accuracy."
                },
                {
                    question: "How does ImpactAI ensure the accuracy of information?",
                    information: [
                        'ImpactAI guarantees accuracy by relying on a structured knowledge base comprised of validated, peer-reviewed research studies. This prevents the generation of unverified content ("hallucinations") and ensures that outputs are both accurate and reliable.',
                        'We can ensure such high-quality standards due to a three-step process:',
                        '1. Verified Evidence Database: We curate a database of evidence-based impact evaluations using our advanced information extraction system, overseen by a team of annotators who manually verify the information.',
                        '2. Natural Language Understanding (NLU): Our NLU module is specifically designed to recognize key concepts from impact evaluation research. It queries our database for precise, context-relevant information.',
                        '3. Summarization: A fine-tuned LLM generates concise, evidence-based answers, strictly constrained to the verified database, preventing hallucinations.',
                        'This process ensures ImpactAI consistently delivers reliable, evidence-driven insights to researchers and policymakers.'
                    ]
                },
            ]
        },
        {
            id: "futuredevelopment",
            headerTitle: "Future Development",
            list1Info: [
                {
                    question: "Will the tool cover research in multiple languages?",
                    information: "Currently, ImpactAI only supports research studies in English. Future updates are expected to include studies in other languages as the tool evolves."
                },
                {
                    question: "Will ImpactAI include an Application Programming Interface (API)?",
                    information: "Yes, ImpactAI will include an API for users who want to dig deeper and use our research data for their analyses."
                },
                {
                    question: "Can ImpactAI be used to predict the results of development interventions that haven't been implemented yet?",
                    information: "While the initial version of ImpactAI does not have this feature, we are working on developing predictive models. There is already some research in this space using off-the-shelf LLMs, but we expect our models to perform better since our model is fine-tuned on actual impact evidence."
                },
                {
                    question: "Can ImpactAI help deliver gaps in existing research in order to develop new research questions?",
                    information: "By providing users with the highest-quality research findings, ImpactAI supports understanding the current research space and can inspire new research questions."
                },
                {
                    question: "Beyond highlighting the effects of certain policy interventions, will ImpactAI be able to identify the reasons why these interventions worked?",
                    information: "Currently, ImpactAI focuses on providing accurate, quantitative insights. Future iterations of the tool are expected to incorporate more qualitative insights to help users understand the context and mechanisms behind successful interventions."
                },
            ]
        },
        {
            id: "stayuptodate",
            headerTitle: "Getting Involved & Staying Updated ",
            list1Info: [
                {
                    question: "How can I stay informed about ImpactAI's development?",
                    information: ["You can sign up for updates on ImpactAI <a href='https://mailchi.mp/worldbank/ai-for-impact?'>here</a> . This will keep you informed about the tool’s launch, new features, and additional research included in our structured database."]
                },
                {
                    question: "How can I provide feedback on ImpactAI?",
                    information: ["We always welcome user feedback to help us improve ImpactAI. You can participate in upcoming beta testing by signing up  <a href='https://mailchi.mp/worldbank/ai-for-impact?'>here</a>  , or submit feedback and suggest new features by contacting <NAME_EMAIL>."
                    ]
                },
            ]
        },
    ];

    useEffect(() => {
        if (chipId) {
            const foundIndex = faqList.findIndex((faq) => faq.id === chipId);

            if (foundIndex !== -1 && faqList[foundIndex].list1Info.length > 0) {
                const panelId = `panel${foundIndex}0`;
                if (expandedAccordion !== panelId) {
                    setExpandedAccordion(panelId);
                }
                if (scrollToAccordion !== panelId) {
                    setScrollToAccordion(panelId);
                }
            }
        }
    }, [chipId]);

    useEffect(() => {
        if (scrollToAccordion && accordionRefs.current[scrollToAccordion]) {
            accordionRefs.current[scrollToAccordion]!.scrollIntoView({ behavior: 'smooth', block: 'start' });
            setScrollToAccordion(null);
        }
    }, [scrollToAccordion]);

    const handleAccordionChange = (panel: string) => (
        event: React.SyntheticEvent,
        isExpanded: boolean
    ) => {
        setExpandedAccordion(isExpanded ? panel : null);
    };

    const renderListItemText = (text: string) => {
        return text.split(/(<a\s+href=['"][^'"]+['"]>.*?<\/a>)/i).map((part, index: number) => {
            const match = part.match(/<a\s+href=['"]([^'"]+)['"]>(.*?)<\/a>/i);
            if (match) {
                const href = match[1];
                const linkText = match[2];
                return (
                    <Link key={index} href={href} target="_blank" rel="noopener noreferrer">
                        {linkText}
                    </Link>
                );
            }
            return <React.Fragment key={index}>{part}</React.Fragment>;
        });
    };

    return (
        <Paper
            sx={{
                padding: "0px",
                height: "auto",
                background: "none",
                boxShadow: 0,
                borderRadius: "8px",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: "35px",
            }}
        >
            <Box
                sx={{
                    width: isMobile ? "97%" : isTablet ? "80%" : "60%",
                    gap: "70px",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                }}
            >
                {faqList.map((faq, index) => (
                    <Box key={index}
                        sx={{
                            width: "100%",
                            display: "flex",
                            flexDirection: "column",
                            justifyContent: "center",
                            alignItems: "center",
                        }}>
                        {/* Section Header */}
                        <Typography variant="h4" sx={{ marginBottom: "25px" }}>
                            {faq.headerTitle}
                        </Typography>

                        {/* Accordions for each question */}
                        {faq.list1Info.map((item, idx) => (
                            <Accordion
                                key={idx}
                                ref={(ref: HTMLDivElement | null) => {
                                    if (ref) {
                                        accordionRefs.current[`panel${index}${idx}`] = ref;
                                    }
                                }}
                                expanded={expandedAccordion === `panel${index}${idx}`}
                                onChange={handleAccordionChange(`panel${index}${idx}`)}
                                component="div"
                                sx={{
                                    width: "100%",
                                    borderRadius: "16px",
                                    backgroundColor: "#F2F6FC",
                                    marginBottom: "10px",
                                    boxShadow: "none",
                                    border: 'none',
                                    transition: 'all 0.3s ease',
                                    "&:hover": {
                                        backgroundColor: "#D4E4FC",
                                    },
                                    "&.MuiAccordion-root:before": {
                                        display: "none"
                                    },
                                    "&.MuiAccordion-root:first-of-type": {
                                        borderRadius: "16px 16px 10px 10px",
                                        background: 'none'
                                    },
                                    "&.MuiAccordion-root:last-of-type": {
                                        borderRadius: "10px 10px 16px 16px",
                                        background: 'none'
                                    },
                                    "&.MuiAccordion-root:first-of-type.MuiAccordion-root:last-of-type": {
                                        borderRadius: "16px",
                                        background: 'none'
                                    },
                                    "&.MuiAccordion-root": {
                                        borderRadius: "16px",
                                        background: 'none'
                                    },
                                }}
                            >
                                <AccordionSummary
                                    expandIcon={<ExpandMoreIcon sx={{ color: '#004370' }} />}
                                    aria-controls={`panel${index}${idx}d-content`}
                                    sx={{
                                        textAlign: "center",
                                        backgroundColor: "#F2F6FC",
                                        borderRadius: "16px",
                                        color: theme.palette.text.primary,
                                        fontSize: "16px",
                                        fontWeight: "bold",
                                        border: 'none',
                                        "&:hover": {
                                            backgroundColor: "#D4E4FC",
                                        },
                                        "&:active": {
                                            backgroundColor: "#D4E4FC",
                                            color: theme.palette.text.primary,
                                        },
                                        "&:focus": {
                                            backgroundColor: "#D4E4FC",
                                            color: theme.palette.text.primary,
                                        },
                                        '&.Mui-expanded': {
                                            backgroundColor: "#F2F6FC",
                                        },
                                    }}
                                >
                                    <Typography>{item.question}</Typography>
                                </AccordionSummary>
                                <AccordionDetails
                                    sx={{
                                        backgroundColor: theme.palette.background.default,
                                        borderRadius: "8px",
                                        padding: "10px",
                                        border: 'none',
                                        background: 'none'
                                    }}
                                >
                                    {Array.isArray(item.information) ? (
                                        <List dense>
                                            {item.information.map((info, infoIdx) => (
                                                <ListItem key={infoIdx}>
                                                    <ListItemText primary={<React.Fragment>{renderListItemText(info)}</React.Fragment>} />
                                                </ListItem>
                                            ))}
                                        </List>
                                    ) : (
                                        <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
                                            {item.information}
                                        </Typography>
                                    )}
                                </AccordionDetails>
                            </Accordion>
                        ))}
                    </Box>
                ))}
            </Box>
        </Paper>
    );
};

export default FaqInfo;