function getEnv(){
  return window.location.hostname === 'localhost' ? 'dev' : 'prod';
}

function getSocketUrl() {
  return 'wss://api.impact-ai-dev.app';
}

function getAuthMessage(token: string, id: string) {
  return JSON.stringify({
    action: 'auth',
    data: {
      token,
      id,
      type: 'user'
    }
  });
}

export function getSubscribeMessage(id: string, type: string = 'conversation') {
  return JSON.stringify({
    action: 'subscribe',
    data: {
      id,
      type
    }
  });
}

export function getPingMessage() {
  return JSON.stringify({
    action: 'ping',
    data: { timestamp: Date.now() }
  });
}

let socketInstance: WebSocket | null = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
const reconnectDelay = 2000;
let pingInterval: NodeJS.Timeout | null = null;
let reconnectTimeout: NodeJS.Timeout | null = null;
let currentSubscription: string | null = null;

export const setCurrentSubscription = (subscriptionId: string) => {
  currentSubscription = subscriptionId;
};

const startHeartbeat = () => {
  if (pingInterval) {
    clearInterval(pingInterval);
  }
  
  pingInterval = setInterval(() => {
    if (socketInstance && socketInstance.readyState === WebSocket.OPEN) {
      socketInstance.send(getPingMessage());
      if (getEnv() === 'dev') {
        console.debug('Sent ping to keep connection alive');
      }
    }
  }, 30000);
};

const stopHeartbeat = () => {
  if (pingInterval) {
    clearInterval(pingInterval);
    pingInterval = null;
  }
};

const attemptReconnect = () => {
  if (reconnectAttempts >= maxReconnectAttempts) {
    if (getEnv() === 'dev') {
      console.error(`Failed to reconnect after ${maxReconnectAttempts} attempts`);
    }
    return;
  }

  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
  }

  const delay = reconnectDelay * Math.pow(1.5, reconnectAttempts);
  
  reconnectTimeout = setTimeout(() => {
    if (getEnv() === 'dev') {
      console.debug(`Attempting to reconnect (${reconnectAttempts + 1}/${maxReconnectAttempts})...`);
    }
    
    getWebsocket();
    reconnectAttempts++;
  }, delay);
};

const resetReconnection = () => {
  reconnectAttempts = 0;
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
    reconnectTimeout = null;
  }
};

export const getWebsocket = () => {
  const socketRoot = getSocketUrl();
  const userData = localStorage.getItem('userData');
  const parsedUserData = userData ? JSON.parse(userData): null;
  const userId = parsedUserData?.user?.id;
  const token = parsedUserData?.token;

  if (socketInstance && socketInstance.readyState !== WebSocket.CLOSED) {
    socketInstance.close();
  }

  socketInstance = new WebSocket(socketRoot);

  socketInstance.onopen = () => {
    if (getEnv() === 'dev') {
      console.debug(`connected-to-${userId}`);
    }

    resetReconnection();
    
    socketInstance?.send(getAuthMessage(token, userId));

    if (getEnv() === 'dev') {
      // eslint-disable-next-line no-console
      console.debug(`authenticated-to-${userId}`);
    }
    
    if (currentSubscription) {
      setTimeout(() => {
        socketInstance?.send(getSubscribeMessage(currentSubscription as string));
        if (getEnv() === 'dev') {
          console.debug(`resubscribed-to-${currentSubscription}`);
        }
      }, 1000);
    }
    
    startHeartbeat();
  };

  socketInstance.onclose = (event) => {
    if (getEnv() === 'dev') {
      // eslint-disable-next-line no-console
      console.debug(`disconnected-from-${userId}`, event.code, event.reason);
    }
    
    stopHeartbeat();
    
    if (event.code !== 1000) {
      attemptReconnect();
    }
  };

  socketInstance.onerror = error => {
    if (getEnv() === 'dev') {
      // eslint-disable-next-line no-console
      console.error('WebSocket error:', error);
    }
  };

  return socketInstance;
};

export const cleanupWebsocket = () => {
  stopHeartbeat();
  
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
    reconnectTimeout = null;
  }
  
  if (socketInstance && socketInstance.readyState !== WebSocket.CLOSED) {
    socketInstance.close(1000);
    socketInstance = null;
  }
};

