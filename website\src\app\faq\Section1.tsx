import React from "react";
import { Paper, Box, Typography, Chip, useTheme } from "@mui/material";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";

interface Section1Props {
    onChipClick: (chipId: string) => void;
}

const Section1: React.FC<Section1Props> = ({ onChipClick }) => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();

    const chipsList = [
        { chipName: "General Information", id: "general" },
        { chipName: "Availability & Access", id: "access" },
        { chipName: "Research & Data Sources", id: "research" },
        { chipName: "Technology & Features", id: "techfeatures" },
        { chipName: "Future Development", id: "futuredevelopment" },
        { chipName: "Getting Involved & Staying Updated", id: "stayuptodate" }
    ];

    return (
        <Paper
            sx={{
                padding: "0px",
                height: "auto",
                background: "none",
                boxShadow: 0,
                borderRadius: "8px",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: "35px",
            }}
        >
            <Box sx={{
                textAlign: "center", width: isMobile ? "95%" : "85%"
            }}>
                <Typography variant={isMobile ? "h1" : isTablet ? "h1" : "h1"} sx={{ pb: 1 }}>
                    Frequently Asked Questions
                </Typography>
                <Typography variant="body1" sx={{ width: isMobile ? "100%" : isTablet ? "100%" : "60%", margin: "auto", textAlign: "center", color: theme.palette.text.secondary }}>
                    Do you have any questions? Here, you’ll find answers to some of the most common questions about ImpactAI, from how it works to when it will be available.
                </Typography>
            </Box>

            <Box
                sx={{
                    display: "flex",
                    flexWrap: "wrap",
                    justifyContent: "center",
                    gap: "8px",
                    flexDirection: isMobile ? 'column' : 'row',
                    alignItems: isMobile ? 'stretch' : 'center',
                    width: isMobile ? "100%" : isTablet ? "90%" : "50%"
                }}
            >
                {chipsList.map((chip, index) => (
                    <Box
                        sx={{
                            minWidth: "auto",
                            whiteSpace: "nowrap",
                            width: isMobile ? '100%' : 'auto',
                        }}
                        key={index}
                    >
                        <Chip
                            label={chip.chipName}
                            onClick={() => onChipClick(chip.id)}
                            sx={{
                                cursor: "pointer",
                                backgroundColor: '#F2F6FC',
                                color : `${theme.palette.primary.main} !important`,
                                height: "38px",
                                fontSize: "14px",
                                padding: "3px 6px",
                                textTransform: "none",
                                width: "100%",
                                borderRadius: '100px',
                                whiteSpace: "normal",
                                overflow: "visible",
                                textOverflow: "clip",
                                "&:hover": {
                                    backgroundColor: "#D4E4FC",
                                    color: theme.palette.text.primary,
                                },
                                "&:focus": {
                                    backgroundColor: "#D4E4FC",
                                    color: theme.palette.text.primary,
                                },
                                "&:active": {
                                    backgroundColor: "#D4E4FC",
                                    color: theme.palette.text.primary,
                                }
                            }}
                        />
                    </Box>
                ))}
            </Box>
        </Paper>
    );
};

export default Section1;