"use client";
import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Grid,
  Typo<PERSON>,
  <PERSON><PERSON>ield,
  Button,
  Alert,
} from "@mui/material";
import { useTheme } from "@mui/material";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";
import DynamicPageTitle from "../components/DynamicPageTitle";
import {
  GoogleReCaptcha,
  GoogleReCaptchaProvider,
} from "react-google-recaptcha-v3";
import { submitWaitlist } from "@/actions/waitlist";
const RECAPTCHA_SITE_KEY = "6Lf65worAAAAAD1HP-QqUYI8cZP5pG0ZR85haRwU"

const JoinTheWaitlist = () => {
  const theme = useTheme();
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const [formData, setFormData] = useState({
    email: "",
    organization: "",
  });
  const [recaptchaToken, setRecaptchaToken] = useState("");
  const [isFormValid, setIsFormValid] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [captchaKey, setCaptchaKey] = useState(Date.now());
  const textFieldStyles = {
    "& .MuiInputBase-root": {
      borderRadius: "4px",
      "&:hover .MuiOutlinedInput-notchedOutline": {
        borderColor: theme.palette.text.primary,
      },
      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
        borderColor: theme.palette.text.primary,
        borderWidth: "1px",
        boxShadow: "none",
      },
    },
    "& .MuiInputLabel-root": {
      color: theme.palette.text.primary,
    },
    "& .MuiOutlinedInput-notchedOutline": {
      border: "1px solid #D4E4FC",
      transition: "border-color 0.3s ease",
    },
    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
      border: "1px solid",
      borderColor: theme.palette.text.primary,
    },
    "&:hover .MuiOutlinedInput-notchedOutline": {
      border: "1px solid",
      borderColor: theme.palette.text.primary,
    },
    "& .MuiInputBase-input::placeholder": {
      color: "rgba(0, 67, 112, 1) !important",
    },
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };
  const handleCaptchaToken = useCallback((token: string) => {
    setRecaptchaToken(token);
  }, []);

  useEffect(() => {
    setIsFormValid(
      Boolean(formData.email && formData.organization && recaptchaToken)
    );
  }, [formData.email, formData.organization, recaptchaToken]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isFormValid) {
      return;
    }

    try {
      const response = await submitWaitlist(formData, recaptchaToken);

      if (response && (response.success === true || response.success === "true")) {
        setFormData({
          email: "",
          organization: "",
        });
        setSuccessMessage("Successfully submitted! We'll be in touch soon.");
        setErrorMessage("");
      } else if (response) {
        let errorMessageToDisplay = "Failed to submit. Please try again.";

        if (response.success === false && Object.keys(response).length === 1) {
          errorMessageToDisplay = "Recaptcha validation failed. Please try again.";
        } else if (response.message) {
          errorMessageToDisplay = response.message;
        }

        setErrorMessage(errorMessageToDisplay);
        setSuccessMessage("");
      } else {
        setErrorMessage("Failed to submit. Please try again.");
        setSuccessMessage("");
      }
    } catch (error: unknown) {
      console.error("Error submitting form:", error);
      setErrorMessage("An error occurred. Please try again.");
      setSuccessMessage("");
    } finally {
      // NEW: Reset reCAPTCHA token and force re-render for next submission
      setRecaptchaToken("");
      setCaptchaKey(Date.now());
    }
  };

  useEffect(() => {
    if (isMobile || isTablet) {
      document.body.style.backgroundImage =
        "url('/images/jointhewaitlist/visual-graphic.png')";
      document.body.style.backgroundSize = "contain";
      document.body.style.backgroundPosition = "center center";
      document.body.style.backgroundRepeat = "no-repeat";
      document.body.style.minHeight = "100vh";

      return () => {
        document.body.style.backgroundImage = "";
        document.body.style.backgroundSize = "";
        document.body.style.backgroundPosition = "";
        document.body.style.backgroundRepeat = "";
      };
    }
  }, [isMobile, isTablet]);

  return (
    <GoogleReCaptchaProvider
      reCaptchaKey={RECAPTCHA_SITE_KEY}
    >
      <DynamicPageTitle
        title="Impact AI - Join the Waitlist"
        description="ImpactAI is revolutionizing global development and empowering development practitioners with a GenAI-powered tool, delivering causal research insights in seconds."
      />
      <Box
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          padding: 0,
          position: "relative",
          overflow: "hidden",
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: "30px",
            padding: 0,
            width: "100%",
            alignItems: "center",
            justifyContent: "center",
            ...(isMobile || isTablet
              ? {
                margin: "0 auto",
              }
              : {}),
          }}
        >
          <Grid container sx={{ mb: 0, p: 0 }}>
            {!(isMobile || isTablet) && (
              <Grid
                item
                xs={12}
                sm={12}
                md={6}
                sx={{
                  p: 0,
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <img
                  src="/images/jointhewaitlist/visual-graphic.png"
                  alt="Network of nodes"
                  style={{ maxWidth: "100%", maxHeight: "400px" }}
                />
              </Grid>
            )}
            <Grid
              item
              xs={12}
              md={isMobile || isTablet ? 12 : 6}
              sx={{
                padding: isMobile ? "20px" : isTablet ? "40px" : "167px 150px",
                display: "flex",
                flexDirection: "column",
                gap: "70px",
                borderRadius: "24px",
                background: "#FFF",
                boxShadow: "0px 0px 250px -20px #E8F0FC",
                p:
                  isMobile || isTablet
                    ? "20px"
                    : "168px 150px 167px 149px !important",
                ...(isMobile || isTablet
                  ? {
                    width: "65%",
                    margin: "20px auto",
                  }
                  : {}),
                my: isMobile ? "24px" : isTablet ? "48px" : "56px",
              }}
            >
              <Box sx={{ textAlign: "justify" }}>
                <Typography
                  variant={isMobile ? "h1" : isTablet ? "h1" : "h1"}
                  sx={{ pb: 1 }}
                >
                  Join the Waitlist!
                </Typography>
                <Typography
                  variant="body1"
                  sx={{ color: theme.palette.text.secondary }}
                >
                  Research evidence to action, in seconds.
                </Typography>
              </Box>
              <form onSubmit={handleSubmit}>
                <Box sx={{ display: "flex", flexDirection: "column", gap: "21px" }}>
                  <TextField
                    label="Email"
                    fullWidth
                    type="email"
                    InputLabelProps={{ shrink: true }}
                    placeholder="<EMAIL>"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    sx={textFieldStyles}
                  />
                  <TextField
                    label="Organization"
                    fullWidth
                    type="text"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Organization Name"
                    name="organization"
                    value={formData.organization}
                    onChange={handleChange}
                    sx={textFieldStyles}
                  />
                  <GoogleReCaptcha key={captchaKey} onVerify={handleCaptchaToken} action="submit" />
                  <Button
                    type="submit"
                    variant="contained"
                    disabled={!isFormValid}
                    sx={{
                      display: "flex",
                      padding: "6px 16px",
                      flexDirection: "column",
                      justifyContent: "center",
                      alignItems: "center",
                      borderRadius: "64px",
                      background: theme.palette.action.active,
                      boxShadow:
                        "0px 1px 5px 0px rgba(0, 0, 0, 0.05), 0px 2px 2px 0px rgba(0, 0, 0, 0.05), 0px 3px 1px -2px rgba(0, 0, 0, 0.05)",
                      color: theme.palette.background.default,
                      textTransform: "none",
                      width: "max-content",
                    }}
                  >
                    Submit
                  </Button>
                  {successMessage && (
                    <Alert
                      severity="success"
                      sx={{
                        width: "100%",
                        mt: 2,
                        color: theme.palette.text.primary,
                        fontWeight: 'bold',
                      }}
                    >
                      {successMessage}
                    </Alert>
                  )}
                  {errorMessage && (
                    <Alert
                      severity="error"
                      sx={{
                        width: "100%",
                        mt: 2,
                        color: theme.palette.text.primary,
                        fontWeight: 'bold',
                      }}
                    >
                      {errorMessage}
                    </Alert>
                  )}
                </Box>
              </form>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </GoogleReCaptchaProvider>
  );
};

export default JoinTheWaitlist;