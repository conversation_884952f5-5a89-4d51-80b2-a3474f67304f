"""Generalizability pipeline for cross-context applicability queries."""

from typing import Dict, Any, List
from src.pipelines.base import Pipeline
from src.tools.manager import ToolManager
from src.pipelines.error_handling import PipelineErrorHandler
from src.pipelines.outputs import create_pipeline_response
import logging

logger = logging.getLogger(__name__)


class GeneralizabilityPipeline(Pipeline):
    """Pipeline for generalizability queries about whether findings apply across different contexts."""

    def __init__(self, tool_manager: ToolManager, config: Dict[str, Any] = None):
        """Initialize the generalizability pipeline."""
        super().__init__(
            name="generalizability_pipeline",
            description="Pipeline for generalizability queries about cross-country comparisons, scalability of interventions, or contextual replication",
            intent="generalizability",
            steps=[
                "entity_extractor",
                "sql_generator",
                "structured_data_organizer",
                "rag_search",
                "final_answer_generator",
            ],
            arguments=[("user_query", "str")],
            outputs="FinalAnswer(text: str, sources: List[str], metadata: Dict[str, Any])",
            config=config,
        )
        self.tool_manager = tool_manager
        self.error_handler = PipelineErrorHandler(
            verbose=self.config.get("verbose", False)
        )

    def get_steps(self) -> List[str]:
        """Get the steps for generalizability analysis."""
        return self.steps

    async def execute(self, user_query: str, **kwargs) -> Dict[str, Any]:
        """Execute the generalizability pipeline."""
        try:
            if self.verbose:
                logger.info(
                    f"Starting generalizability pipeline for query: {user_query}"
                )

            # Step 1: Entity Extraction
            entities = await self._execute_step(
                "entity_extractor", user_query=user_query
            )
            if not self.error_handler.has_meaningful_entities(entities):
                return self.error_handler.handle_no_entities(
                    "generalizability", user_query, self.name
                )

            # Step 2: SQL Generation and Execution
            query_result = await self._execute_step("sql_generator", entities=entities)
            if query_result.row_count == 0:
                return self.error_handler.handle_no_data_found(
                    "generalizability", query_result, user_query, self.name
                )

            # Step 3: Structured Data Organization
            structured_data = await self._execute_step(
                "structured_data_organizer",
                user_query=user_query,
                entities=entities,
                dataset=query_result.dataset,
            )

            # Step 4: RAG Search for contextual information
            rag_results = await self._execute_step(
                "rag_search", query_result=query_result, num_results=15
            )

            # Step 5: Final Answer Generation
            final_answer = await self._execute_step(
                "final_answer_generator",
                user_query=user_query,
                structured_data=structured_data,
            )

            return create_pipeline_response(
                intent="generalizability",
                status="completed_successfully",
                user_query=user_query,
                observation=final_answer.text,
                answer=final_answer.text,
                pipeline_name=self.name,
                thought=f"Successfully executed generalizability pipeline with {query_result.row_count} data points from {query_result.unique_papers} papers, including contextual information from RAG search. The analysis provides comprehensive generalizability information.",
                steps_completed=self.get_steps(),
                data_points=query_result.row_count,
                papers_analyzed=query_result.unique_papers,
                rag_passages=len(rag_results.source_documents) if rag_results else 0,
            )

        except Exception as e:
            return self.error_handler.handle_pipeline_error(
                "generalizability", e, user_query, self.name
            )

    async def _execute_step(self, step_name: str, **kwargs):
        """Execute a pipeline step with error handling and tracking."""
        try:
            self.current_step += 1
            if self.verbose:
                logger.info(f"Step {self.current_step}: Executing {step_name}")

            # Start tracking the step
            self._track_step_start(step_name, kwargs)

            result = await self.tool_manager.execute_with_cache(step_name, **kwargs)
            self.results[step_name] = result

            # Track successful completion
            output_summary = {"type": type(result).__name__}
            if hasattr(result, "row_count"):
                output_summary["row_count"] = result.row_count
            if hasattr(result, "text"):
                output_summary["text_length"] = len(result.text)

            self._track_step_end(success=True, output_data=output_summary)

            return result

        except Exception as e:
            if self.verbose:
                logger.error(f"Error in {step_name}: {e}")

            # Track the error
            self._track_step_end(success=False, error_message=str(e))
            raise
