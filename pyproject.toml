[tool.black]
line-length = 88
target-version = ['py37', 'py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '\.ipynb$'

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.ruff]
lint.select = ["F401"]  # Detect unused imports
fix = true              # Autofix by default
line-length = 88        # Match Black's default
target-version = "py311"

[tool.ruff.format]
# Use Black-compatible formatting
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.pytest.ini_options]
pythonpath = ["backend"]
testpaths = ["backend"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
