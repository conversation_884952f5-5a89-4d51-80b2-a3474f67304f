import React, { useState, useEffect } from "react";
import { Grid, Box, Typography } from "@mui/material";
import { Message, Source, PlotData, Option } from "../../../types/ConversationTypes";
import Markdown from "markdown-to-jsx";
import StreamingSummary from "./Streaming/StreamingSummary";
import { renderTextWithTags, formatSummaryText } from './Streaming/Utils';
import LinkComponent from "./Streaming/LinkComponent";
import './SummarySection.css';
import { NormalizeSpaces } from "../../../utils/helpers";

interface SummarySectionProps {
  messageId: string;
  summary: Message;
  conversationId: string;
  informationMessageId: string;
  onViewOnPlotClicked: (payload: { citation_ids: string[]; messageId: string }) => void;
  onViewOnPlotHover: (payload: { citation_ids: string[]; messageId: string }) => void;
  onViewOnSourceHover: (payload: { paper_ids: string[]; messageId: string }) => void;
  onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
  setDisplaySystemLoader: (flag: boolean) => void;
  setStreamingEnded: (flag: boolean) => void;
  setSummaryStreamedText: (text: any) => void;
  setHasStreamingStartedForDataFetch: (flag: boolean) => void;
  setStreamedTextFromHook: (text: string) => void;
  setFetchedSourcesData: (sources: Source[]) => void;
  setFetchedPlotData: (data: any) => void;
  setRelatedTopics: (data: Option[]) => void;
}

const SummarySection: React.FC<SummarySectionProps> = ({
  messageId,
  summary,
  conversationId,
  informationMessageId,
  onViewOnPlotClicked,
  onViewOnPlotHover,
  onViewOnSourceHover,
  onViewOnSourceClicked,
  setDisplaySystemLoader,
  setStreamingEnded,
  setSummaryStreamedText,
  setHasStreamingStartedForDataFetch,
  setStreamedTextFromHook,
  setFetchedSourcesData,
  setFetchedPlotData,
  setRelatedTopics
}) => {
  const [displayText, setDisplayText] = useState("");
  const [sources, setSources] = useState<Source[]>([]);
  const [plotData, setPlotData] = useState<PlotData[]>([]);

  useEffect(() => {
    if (summary.text && summary.text.length > 0) {
      setDisplayText(formatSummaryText(summary.text));
    }
  }, [summary]);

  useEffect(() => {
    const updatedSources = summary?.sources ?? [];
    const updatedPlotData = summary?.plot?.data ?? [];

    setSources(updatedSources);
    setPlotData(updatedPlotData);
  }, [summary?.sources, summary?.plot?.data]);

  const renderMarkdownContent = (text: string) => (
    <>
      <Box sx={{ display: 'flex', alignItems: text.trim().length >= 80 ? "flex-start" : "center" }}>
        {text.trim().length > 0 && (
          <Box
            sx={{
              width: 32,
              height: 32,
              marginRight: 2,
              flexShrink: 0,
              backgroundImage: `url('/logo_icon.svg')`,
              backgroundSize: 'contain',
              backgroundRepeat: 'no-repeat',
            }}
          />
        )}
        <Markdown
          className="markdown-container"
          options={{
            overrides: {
              // TODO: Fixes "div in p" hydration error.
              p: {
                component: 'div',
                props: {
                  // When 'component' is a string HTML tag (like 'div'),
                  // this 'props.component' is redundant and has no effect on the rendered element.
                  component: 'div',
                },
              },
              a: {
                component: LinkComponent, props: {
                  messageId: messageId,
                  onViewOnPlotClicked: onViewOnPlotClicked,
                  onViewOnPlotHover: onViewOnPlotHover,
                  onViewOnSourceHover: onViewOnSourceHover,
                  onViewOnSourceClicked: onViewOnSourceClicked
                }
              }
            }
          }}
        >
          {renderTextWithTags(text, sources, plotData)}
        </Markdown>
      </Box>
    </>
  );

  return (
    <Grid container spacing={0} id={`/conversations/${conversationId}`} summary-trigger={messageId}>
      <Grid item xs={12}>
        <Box sx={{ p: 0, display: "flex", alignItems: displayText.length > 80 ? "flex-start" : "center" }}>
          <Box component="div" className="markdown-container" sx={{ flex: 1 }}>
            <Typography variant="body1" component="div" fontSize={16} sx={{ textAlign: "justify", letterSpacing: "0.15px", lineHeight: "150%" }}>
              {summary.id && summary.id === 'system-loading' ? (
                <StreamingSummary
                  conversationId={conversationId}
                  informationMessageId={informationMessageId}
                  setDisplaySystemLoader={setDisplaySystemLoader}
                  setStreamingEnded={setStreamingEnded}
                  setSummaryStreamedText={setSummaryStreamedText}
                  onViewOnPlotClicked={onViewOnPlotClicked}
                  onViewOnPlotHover={onViewOnPlotHover}
                  onViewOnSourceHover={onViewOnSourceHover}
                  onViewOnSourceClicked={onViewOnSourceClicked}
                  messageId={messageId}
                  setDisplayText={setDisplayText}
                  setHasStreamingStartedForDataFetch={setHasStreamingStartedForDataFetch}
                  setStreamedTextFromHook={setStreamedTextFromHook}
                  setFetchedSourcesData={setFetchedSourcesData}
                  setFetchedPlotData={setFetchedPlotData}
                  setRelatedTopics={setRelatedTopics}
                />
              ) : (
                renderMarkdownContent(NormalizeSpaces(displayText))
              )}
            </Typography>
          </Box>
        </Box>
      </Grid>
    </Grid>
  );
};

export default SummarySection;