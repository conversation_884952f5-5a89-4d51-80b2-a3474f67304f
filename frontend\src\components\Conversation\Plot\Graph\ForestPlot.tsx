import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  useTheme,
  Slider,
  Select,
  MenuItem,
  ToggleButtonGroup,
  Chip,
  Stack,
  OutlinedInput,
  ListItemText,
  Checkbox,
} from "@mui/material";
import { FilterAlt, Clear } from "@mui/icons-material";
import EffectSizesPlot from "./EffectSizesPlot";
import Outcomes from "./Outcomes";
import Interventions from "./Interventions";
import StudyDetails from "./StudyDetails";
import EffectSizesPlot2 from "./EffectSizesPlot2";
import { Outcome } from "./ForestPlotJWT.types";
import * as d3 from "d3";
import "./ForestPlot.css";

interface ForestPlotProps {
  plotData: any;
  studiesData: any;
  selectedOutcome: string;
  selectedIntervention: string;
  hoveredPair: { intervention: string; outcome: string } | undefined;
  hoveredIntervention: { intervention: string } | undefined;
  hoveredOutcome: { outcome: string } | undefined;
}

const ForestPlot: React.FC<ForestPlotProps> = ({
  plotData,
  studiesData,
  selectedOutcome,
  selectedIntervention,
  hoveredPair,
}: ForestPlotProps) => {
  const theme = useTheme();

  const uniqueOutcomes = plotData.flat_effect_sizes
    .reduce((acc, cur) => {
      const outcomeTagId = cur.outcome_tag_ids[0];
      if (!acc.some((outcome) => outcome.outcome_tag_id === outcomeTagId)) {
        acc.push({
          outcome_tag_id: outcomeTagId,
          label: cur.outcome_tag_short_labels,
        });
      }
      return acc;
    }, [])
    .sort((a, b) => a.label.localeCompare(b.label));

  const [selectedOutcome2, setSelectedOutcome2] = useState<Outcome>(
    uniqueOutcomes[0]
  );

  const [activePanel, setActivePanel] = useState<string>("");
  const [hoveredIntervention, setHoveredIntervention] = useState<
    number | undefined
  >(undefined);

  if (!plotData) {
    return null;
  }

  const yearExtent = d3.extent(
    plotData.flat_effect_sizes.filter((d) =>
      d.outcome_tag_ids.includes(
        selectedOutcome2?.outcome_tag_id || selectedOutcome2
      )
    ),
    (d) => d.year
  );

  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
  const [selectedSectors, setSelectedSectors] = useState<string[]>([]);
  const [selectedIncomeGroups, setSelectedIncomeGroups] = useState<string[]>(
    []
  );
  const [publicationYear2, setPublicationYear2] = useState<
    number[] | undefined[] | string[]
  >(yearExtent);
  const [selectedQualityScore, setSelectedQualityScore] = useState<string[]>(
    []
  );
  console.log(plotData, "plotData");
  const bySelectedOutcome3 = plotData.flat_effect_sizes.filter(
    (d) =>
      d.outcome_tag_ids.includes(
        selectedOutcome2?.outcome_tag_id || selectedOutcome2
      ) &&
      (selectedSectors.length === 0
        ? true
        : selectedSectors.some((sector) =>
            d.intervention_sectors.includes(sector)
          )) &&
      (selectedRegions.length === 0
        ? true
        : selectedRegions.some((region) => d.region.includes(region))) &&
      (selectedIncomeGroups.length === 0
        ? true
        : selectedIncomeGroups.includes(d.income_group)) &&
      (publicationYear2[0] === publicationYear2[1]
        ? true
        : d.year >= publicationYear2[0] && d.year <= publicationYear2[1]) &&
      (selectedQualityScore.length === 0
        ? true
        : selectedQualityScore.includes(d.quality_score_category))
  );

  const allInterventionsByForSelectedOutcome =
    plotData.flat_effect_sizes.filter((d) =>
      d.outcome_tag_ids.includes(
        selectedOutcome2?.outcome_tag_id || selectedOutcome2
      )
    );
  const xDomain = [
    Math.min(
      0,
      d3.min(
        allInterventionsByForSelectedOutcome,
        (d) => d.standardized_ci_lower
      )
    ),
    Math.max(
      0,
      d3.max(
        allInterventionsByForSelectedOutcome,
        (d) => d.standardized_ci_upper
      )
    ),
  ];

  const byIntervention = bySelectedOutcome3.reduce((acc, cur) => {
    cur.intervention_tag_short_labels.forEach((tag) => {
      if (!acc[tag]) {
        acc[tag] = [];
      }
      acc[tag].push(cur);
    });

    return acc;
  }, {});

  const byInterventionArray = Object.entries(byIntervention).map(
    ([key, value]) => [key, value]
  );
  console.log("byInterventionArray", byInterventionArray);

  const [interventions, setInterventions] = useState<{ id: any; label: any }[]>(
    []
  );
  const [plotDataProcessed, setPlotDataProcessed] = useState<any>(null);

  useEffect(() => {
    if (plotData && Array.isArray(plotData) && plotData.length > 0) {
      setPlotDataProcessed(plotData);
    } else {
      setPlotDataProcessed(null);
    }
  }, [plotData]);

  // useEffect(() => {
  //   if (plotDataProcessed) {
  //     const outcomesData = d3
  //       .groups(plotDataProcessed, (d) => d.outcome)
  //       .map((outcome) => ({
  //         id: outcome[1][0]?.outcome_id,
  //         label: outcome[0],
  //         studies: outcome[1]
  //           ?.map((d) => d.data)
  //           ?.flat()
  //           ?.sort((a, b) => (b?.score?.value || -Infinity) - (a?.score?.value || -Infinity)),
  //       }))
  //       .filter((outcome) => outcome.id !== undefined && outcome.studies !== undefined);

  //     // setByOutcome(outcomesData);

  //     if (outcomesData.length > 0 && !selectedOutcome2) {
  //       setSelectedOutcome2(outcomesData[0]);
  //     }
  //   } else {
  //     // setByOutcome([]);
  //     setSelectedOutcome2(undefined);
  //   }
  // }, [plotDataProcessed, selectedOutcome2]);

  useEffect(() => {
    if (selectedOutcome) {
      setActivePanel(selectedOutcome);
    }
  }, [selectedOutcome]);

  useEffect(() => {
    if (selectedOutcome2?.id && plotDataProcessed) {
      const interventionData = d3
        .groups(
          plotDataProcessed.filter((d) => d.outcome_id === selectedOutcome2.id),
          (d) => d.intervention
        )
        .map((intervention) => ({
          id: intervention[1][0]?.intervention_id,
          label: intervention[0],
        }))
        .filter(
          (intervention) =>
            intervention.id !== undefined && intervention.label !== undefined
        );
      setInterventions(interventionData);
    } else {
      setInterventions([]);
    }
  }, [plotDataProcessed, selectedOutcome2?.id]);

  const handleOnOutcomeClicked = (id) => {
    const outcome = uniqueOutcomes.find((d) => d.outcome_tag_id === id);
    console.log("handleOnOutcomeClicked", outcome, id);
    if (outcome) {
      setSelectedOutcome2(outcome.outcome_tag_id);
      console.log(yearExtent, "yearExtent");

      setSelectedSectors([]);
      setSelectedRegions([]);
      setSelectedIncomeGroups([]);
      setPublicationYear2(yearExtent);
      setSelectedQualityScore([]);
      setSectorFilterEnabled(false);
      setRegionFilterEnabled(false);
      setIncomeGroupFilterEnabled(false);
      setPublicationYearFilterEnabled(false);
      setQualityScoreFilterEnabled(false);
    }
  };

  const handleOnInterventionPointerOut = () => {
    setHoveredIntervention(undefined);
  };

  const handleOnInterventionPointerOver = (interventionId) => {
    setHoveredIntervention(interventionId);
  };

  // if (!plotDataProcessed) {
  //   return (
  //     <Box
  //       sx={{
  //         display: "flex",
  //         justifyContent: "center",
  //         alignItems: "center",
  //         minHeight: "200px",
  //         borderRadius: "8px",
  //         border: `1px solid ${theme.elevation.outlined}`,
  //         backgroundColor: `${theme.palette.background.default}`,
  //         width: "100%",
  //         maxWidth: "100%",
  //       }}
  //     >
  //       <Typography color="textSecondary">No plot data available.</Typography>
  //     </Box>
  //   );
  // }

  // if (!selectedOutcome2) {
  //   return (
  //     <Box
  //       sx={{
  //         display: "flex",
  //         justifyContent: "center",
  //         alignItems: "center",
  //         minHeight: "200px",
  //         borderRadius: "8px",
  //         border: `1px solid ${theme.elevation.outlined}`,
  //         backgroundColor: `${theme.palette.background.default}`,
  //         width: "100%",
  //         maxWidth: "100%",
  //       }}
  //     >
  //       <Typography color="textSecondary">No outcomes available in the data.</Typography>
  //     </Box>
  //   );
  // }

  // ----- SECTORS -----

  const allSectors2 = Array.from(
    new Set([
      ...plotData.flat_effect_sizes
        .filter((d) =>
          d.outcome_tag_ids.includes(
            selectedOutcome2?.outcome_tag_id || selectedOutcome2
          )
        )
        .map((d) => d.all_sectors)
        .flat(),
    ])
  );
  console.log(allSectors2, selectedOutcome2, "sdhsjhdjshdj");
  // .sort((a, b) => a.localeCompare(b));

  const [sectorFilterEnabled, setSectorFilterEnabled] = useState(false);

  const handleSectorChipClick = () => {
    if (sectorFilterEnabled) {
      setSelectedSectors([]);
    }
    setSectorFilterEnabled(!sectorFilterEnabled);
  };

  const handleSectorChange2 = (value: string | string[]) => {
    setSelectedSectors(typeof value === "string" ? value.split(",") : value);
  };

  // ----- REGIONS -----

  const allRegions = Array.from(
    new Set([
      ...plotData.flat_effect_sizes
        .filter((d) =>
          d.outcome_tag_ids.includes(
            selectedOutcome2?.outcome_tag_id || selectedOutcome2
          )
        )
        .map((d) => d.region || "N/A"),
    ])
  ).sort((a, b) => a.localeCompare(b));

  const [regionFilterEnabled, setRegionFilterEnabled] = useState(false);

  const handleRegionChipClick = () => {
    if (regionFilterEnabled) {
      setSelectedRegions([]);
    }
    setRegionFilterEnabled(!regionFilterEnabled);
  };

  const handleRegionChange = (value: string | string[]) => {
    setSelectedRegions(typeof value === "string" ? value.split(",") : value);
  };

  // ----- INCOME GROUPS -----

  const incomeGroupOrder = [
    "High income",
    "Upper middle income",
    "Lower middle income",
    "Low income",
  ];

  const allIncomeGroups = Array.from(
    new Set([
      ...plotData.flat_effect_sizes
        .filter((d) =>
          d.outcome_tag_ids.includes(
            selectedOutcome2?.outcome_tag_id || selectedOutcome2
          )
        )
        .map((d) => d.income_group),
    ])
  ).sort((a, b) => incomeGroupOrder.indexOf(a) - incomeGroupOrder.indexOf(b));

  const [incomeGroupFilterEnabled, setIncomeGroupFilterEnabled] =
    useState(false);

  const handleIncomeGroupChipClick = () => {
    if (incomeGroupFilterEnabled) {
      setSelectedIncomeGroups([]);
    }
    setIncomeGroupFilterEnabled(!incomeGroupFilterEnabled);
  };

  const handleIncomeGroupChange = (value: string | string[]) => {
    setSelectedIncomeGroups(
      typeof value === "string" ? value.split(",") : value
    );
  };

  // ----- PUBLICATION YEAR -----

  const [publicationYearFilterEnabled, setPublicationYearFilterEnabled] =
    useState(false);

  const handlePublicationYearChange2 = (
    event: Event,
    newValue: number | number[]
  ) => {
    setPublicationYear2(newValue as number[]);
    setPublicationYearFilterEnabled(true);
  };
  const handlePublicationYearChipClick = () => {
    if (publicationYearFilterEnabled) {
      setPublicationYear2(yearExtent);
    }
    setPublicationYearFilterEnabled(!publicationYearFilterEnabled);
  };

  // ----- QUALITY SCORE -----
  const qualityScoreOrder = ["Low", "Average", "High"];

  const allQualityScoreGroups = Array.from(
    new Set(
      plotData.flat_effect_sizes
        .filter((d) =>
          d.outcome_tag_ids.includes(
            selectedOutcome2?.outcome_tag_id || selectedOutcome2
          )
        )
        .map((d) => d.quality_score_category)
    )
  ).sort((a, b) => qualityScoreOrder.indexOf(a) - qualityScoreOrder.indexOf(b));

  const [qualityScoreGroup, setQualityScoreGroup] = useState<
    string | undefined
  >();

  const [qualityScoreFilterEnabled, setQualityScoreFilterEnabled] =
    useState(false);
  const handleQualityScoreChipClick = () => {
    if (qualityScoreFilterEnabled) {
      setSelectedQualityScore([]);
    }
    setQualityScoreFilterEnabled(!qualityScoreFilterEnabled);
  };

  const handleQualityScoreChange = (value: string) => {
    console.log("VALUE", value);
    if (selectedQualityScore.includes(value)) {
      setSelectedQualityScore(
        selectedQualityScore.filter((item) => item !== value)
      );
    } else {
      setSelectedQualityScore((prev) => [...prev, value]);
    }
  };

  // -----

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        borderRadius: "8px",
        border: `1px solid ${theme.elevation.outlined}`,
        backgroundColor: `${theme.palette.background.default}`,
        overflow: "hidden",
        width: "100%",
        maxWidth: "100%",
        [theme.breakpoints.down("sm")]: {
          padding: "8px",
        },
      }}
    >
      <div>
        <Outcomes
          outcomes={uniqueOutcomes}
          selectedOutcome={selectedOutcome2}
          onOutcomeClicked={handleOnOutcomeClicked}
        />
        <h4
          style={{
            marginTop: 20,
            marginBottom: 20,
            fontWeight: "normal",
            textAlign: "center",
          }}
        >
          Effect sizes
        </h4>
        <div
          style={{  maxHeight: 300, overflowY: "auto" }}
        >
          {byInterventionArray
            .sort(
              (a, b) =>
                (b[1][0].outcome_connotation === "Negative" ? -1 : 1) *
                  d3.mean(b[1], (d) => d.cohen_d) -
                (a[1][0].outcome_connotation === "Negative" ? -1 : 1) *
                  d3.mean(a[1], (d) => d.cohen_d)
            )
            .map((d) => (
              <EffectSizesPlot2 data={d} xDomain={xDomain} />
            ))}

          <h5
            style={{ width: "100%", textAlign: "center", fontWeight: "normal" }}
          >
            Effect Size (in Standardized Mean Difference)
          </h5>
        </div>
        <div
          style={{
            paddingInline: 20,
            paddingBottom: 20,
            backgroundColor: "white",
          }}
        >
          <div className="filter-chips" style={{ paddingBlock: 20 }}>
            <div
              style={{
                fontSize: 15,
                display: "flex",
                alignItems: "center",
                paddingBottom: 10,
              }}
            >
              <FilterAlt style={{ fontSize: 20 }} /> Filter By:
            </div>
            <Stack
              direction="row"
              spacing={1}
              useFlexGap={true}
              flexWrap="wrap"
            >
              <Chip
                onClick={handleSectorChipClick}
                label="Sector"
                disabled={false}
                variant="outlined"
                size="small"
                color="primary"
                icon={sectorFilterEnabled && <Clear />}
              />
              <Chip
                onClick={handleRegionChipClick}
                label="Region"
                disabled={false}
                variant="outlined"
                size="small"
                color="primary"
                icon={regionFilterEnabled && <Clear />}
              />
              <Chip
                onClick={handleIncomeGroupChipClick}
                label="Income Group"
                disabled={false}
                variant="outlined"
                size="small"
                color="primary"
                icon={incomeGroupFilterEnabled && <Clear />}
              />
              <Chip
                onClick={handlePublicationYearChipClick}
                label="Publication Year"
                disabled={false}
                variant="outlined"
                size="small"
                color="primary"
                icon={publicationYearFilterEnabled && <Clear />}
              />
              <Chip
                onClick={handleQualityScoreChipClick}
                label="Quality Score"
                disabled={false}
                variant="outlined"
                size="small"
                color="primary"
                icon={qualityScoreFilterEnabled && <Clear />}
              />
            </Stack>
          </div>

          {sectorFilterEnabled && (
            <div style={{ marginTop: 10 }}>
              {allSectors2.length === 1 && (
                <div style={{ fontSize: 15 }}>
                  <Typography>Sector: {allSectors2[0]}</Typography>
                </div>
              )}
              {allSectors2.length > 1 && (
                <div>
                  <div style={{ fontSize: 15 }}>Sector:</div>
                  <Select
                    size="small"
                    labelId="dropdown-label"
                    id="dropdown"
                    value={selectedSectors}
                    multiple
                    style={{ maxWidth: "100%", width: "100%" }}
                    onChange={(e) => {
                      handleSectorChange2(e.target.value);
                    }}
                    label="Select an Option"
                    input={
                      <OutlinedInput id="select-multiple-chip" label="Chip" />
                    }
                    renderValue={(selected) => selected.join(", ")}
                  >
                    {allSectors2.map((s) => (
                      <MenuItem key={s} value={s}>
                        <Checkbox checked={selectedSectors.includes(s)} />
                        <ListItemText primary={s} />
                      </MenuItem>
                    ))}
                  </Select>
                </div>
              )}
            </div>
          )}
          {regionFilterEnabled && (
            <div style={{ marginTop: 10 }}>
              {allRegions.length === 1 && (
                <div style={{ fontSize: 15 }}>
                  <Typography>Region: {allRegions[0]}</Typography>
                </div>
              )}
              {allRegions.length > 1 && (
                <div>
                  <div style={{ fontSize: 15 }}>Region:</div>
                  <Select
                    size="small"
                    labelId="dropdown-label"
                    id="dropdown"
                    value={selectedRegions}
                    multiple
                    style={{ maxWidth: "100%", width: "100%" }}
                    onChange={(e) => {
                      handleRegionChange(e.target.value);
                    }}
                    label="Select an Option"
                    input={
                      <OutlinedInput id="select-multiple-chip" label="Chip" />
                    }
                    renderValue={(selected) => selected.join(", ")}
                  >
                    {allRegions.map((s) => (
                      <MenuItem key={s} value={s}>
                        <Checkbox checked={selectedRegions.includes(s)} />
                        <ListItemText primary={s} />
                      </MenuItem>
                    ))}
                  </Select>
                </div>
              )}
            </div>
          )}
          {incomeGroupFilterEnabled && (
            <div style={{ marginTop: 10 }}>
              {allIncomeGroups.length === 1 && (
                <div style={{ fontSize: 15 }}>
                  <Typography>Income Group: {allIncomeGroups[0]}</Typography>
                </div>
              )}
              {allIncomeGroups.length > 1 && (
                <div>
                  <div style={{ fontSize: 15 }}>Income Group:</div>
                  <Select
                    size="small"
                    labelId="dropdown-label"
                    id="dropdown"
                    value={selectedIncomeGroups}
                    multiple
                    style={{ maxWidth: "100%", width: "100%" }}
                    onChange={(e) => {
                      // setSector(e.target.value);
                      handleIncomeGroupChange(e.target.value);
                    }}
                    label="Select an Option"
                    input={
                      <OutlinedInput id="select-multiple-chip" label="Chip" />
                    }
                    renderValue={(selected) => selected.join(", ")}
                  >
                    {allIncomeGroups.map((s) => (
                      <MenuItem key={s} value={s}>
                        <Checkbox checked={selectedIncomeGroups.includes(s)} />
                        <ListItemText primary={s} />
                      </MenuItem>
                    ))}
                  </Select>
                </div>
              )}
            </div>
          )}
          {publicationYearFilterEnabled && (
            <div style={{ marginTop: 10 }}>
              {publicationYear2[0] === publicationYear2[1] && (
                <div style={{ fontSize: 15 }}>
                  <Typography>
                    Publication Year: {publicationYear2[0]}
                  </Typography>
                </div>
              )}
              {publicationYear2[0] !== publicationYear2[1] && (
                <div>
                  <div style={{ fontSize: 15 }}>Publication Year:</div>
                  <Slider
                    aria-label="PublicationYear"
                    value={publicationYear2}
                    onChange={handlePublicationYearChange2}
                    min={yearExtent[0]}
                    max={yearExtent[1]}
                    marks={true}
                    step={1}
                    size="small"
                  />
                  <div
                    style={{ display: "flex", justifyContent: "space-between" }}
                  >
                    <div style={{ fontSize: "12px" }}>
                      {publicationYear2[0]}
                    </div>
                    <div style={{ fontSize: "12px" }}>
                      {publicationYear2[1]}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          {qualityScoreFilterEnabled && (
            <div style={{ marginTop: 10 }}>
              {allQualityScoreGroups.length === 1 && (
                <div>Quality Score: {allQualityScoreGroups[0]}</div>
              )}
              {allQualityScoreGroups.length > 1 && (
                <div>
                  <div style={{ fontSize: 15 }}>Quality Score:</div>
                  <ToggleButtonGroup size="small" value={qualityScoreGroup}>
                    <Stack
                      direction="row"
                      spacing={1}
                      useFlexGap={true}
                      flexWrap="wrap"
                    >
                      {allQualityScoreGroups.map((q) => (
                        <Chip
                          onClick={() => handleQualityScoreChange(q)}
                          size="small"
                          label={q}
                          disabled={false}
                          style={{
                            backgroundColor: selectedQualityScore.includes(q)
                              ? "#D4E4FC"
                              : null,
                          }}
                        />
                      ))}
                    </Stack>
                  </ToggleButtonGroup>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </Box>
  );
};

export default ForestPlot;
