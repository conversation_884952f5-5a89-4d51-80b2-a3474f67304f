import React from "react";
import { Paper, Box, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import JoinWaitlistButton from "../components/JoinWaitlistButton";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";

const JoinWaitlistSection = () => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    return (
        <Paper
            sx={{
                padding: "0px",
                width: "100%",
                height: isMobile ? "254px" : isTablet ? "254px" : "389px",
                boxShadow: 0,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                gap: "0px",
                borderRadius: "32px",
                background: "#E8F0FC",
            }}
        >
            {/* Header Section */}
            <Box
                sx={{
                    textAlign: "center",
                    display: "flex",
                    justifyContent: "center",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: "20px"
                }}>
                <Box sx={{
                    textAlign: "center",
                    display: "flex",
                    justifyContent: "center",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: '8px'
                }}>
                    <Typography variant={isMobile ? "h3" : isTablet ? "h2" : "h2"}>
                        Interested? Give it a go!
                    </Typography>
                    <Typography variant={isMobile ? "body2" : isTablet ? "body2" : "body1"} sx={{ width: "85%", textAlign: "center", color: theme.palette.text.secondary }}>
                        Imagine you’re a policymaker tasked with distributing limited resources to achieve maximum impact. How do you decide where funding will have the greatest effect? ImpactAI synthesizes studies to make this decision-making process faster and more effective.
                    </Typography>
                </Box>
                <JoinWaitlistButton />
            </Box>
        </Paper>
    );
};

export default JoinWaitlistSection;
