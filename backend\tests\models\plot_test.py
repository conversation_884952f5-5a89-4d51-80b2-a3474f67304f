from models.plot import (
    TagWithLevel,
    Score,
    EffectSizeDataPoint,
    EffectSize,
    Study,
    DefaultTabType,
    RawEffectSize,
    RawIntervention,
    RawOutcome,
    PlotData,
    PlotType,
    Plot,
)


class TestTagWithLevel:
    """Test suite for TagWithLevel class"""

    def test_tag_with_level_creation(self):
        tag = TagWithLevel(tag_label="education", level=1)
        assert tag.tag_label == "education"
        assert tag.level == 1


class TestScore:
    """Test suite for Score class"""

    def setup_method(self):
        """Setup for each test"""
        self.score = Score(lower=0.1, upper=0.9, value=0.5)

    def test_score_creation(self):
        assert self.score.lower == 0.1
        assert self.score.upper == 0.9
        assert self.score.value == 0.5


class TestEffectSizeDataPoint:
    """Test suite for EffectSizeDataPoint class"""

    def setup_method(self):
        """Setup for each test"""
        self.score = Score(lower=0.1, upper=0.9, value=0.5)
        self.data_point = EffectSizeDataPoint(
            label="Test Effect",
            score=self.score,
            country="USA",
            paper_id=123,
            paper_title="Test Paper",
            paper_citation="Author (2023)",
        )

    def test_effect_size_data_point_creation(self):
        assert self.data_point.label == "Test Effect"
        assert self.data_point.score == self.score
        assert self.data_point.country == "USA"
        assert self.data_point.paper_id == 123
        assert self.data_point.paper_title == "Test Paper"
        assert self.data_point.paper_citation == "Author (2023)"


class TestEffectSize:
    """Test suite for EffectSize class"""

    def setup_method(self):
        """Setup for each test"""
        self.score = Score(lower=0.1, upper=0.9, value=0.5)
        self.data_point = EffectSizeDataPoint(
            label="Test Effect",
            score=self.score,
            country="USA",
            paper_id=123,
            paper_title="Test Paper",
            paper_citation="Author (2023)",
        )
        self.effect_size = EffectSize(
            data=[self.data_point],
            outcome="Education",
            outcome_id=1,
            intervention="Training",
            intervention_id=2,
            aggregate=self.score,
        )

    def test_effect_size_creation(self):
        assert len(self.effect_size.data) == 1
        assert self.effect_size.outcome == "Education"
        assert self.effect_size.outcome_id == 1
        assert self.effect_size.intervention == "Training"
        assert self.effect_size.intervention_id == 2
        assert self.effect_size.aggregate == self.score


class TestStudy:
    """Test suite for Study class"""

    def setup_method(self):
        """Setup for each test"""
        self.study = Study(
            id="123",
            label="Test Study",
            pulication_year=2023,
            first_author="Test Author",
            citation="Test Author (2023)",
            population=1000,
            country="USA",
            region_code="NA",
            region_label="North America",
            quality_score=8,
            quality_score_group="High",
            income_group_code="HI",
            income_group_label="High Income",
            sector="Education",
            journal_name="Test Journal",
        )

    def test_study_creation(self):
        assert self.study.id == "123"
        assert self.study.label == "Test Study"
        assert self.study.pulication_year == 2023
        assert self.study.first_author == "Test Author"
        assert self.study.citation == "Test Author (2023)"
        assert self.study.population == 1000
        assert self.study.country == "USA"
        assert self.study.region_code == "NA"
        assert self.study.region_label == "North America"
        assert self.study.quality_score == 8
        assert self.study.quality_score_group == "High"
        assert self.study.income_group_code == "HI"
        assert self.study.income_group_label == "High Income"
        assert self.study.sector == "Education"
        assert self.study.journal_name == "Test Journal"


class TestDefaultTabType:
    """Test suite for DefaultTabType enum"""

    def test_default_tab_type_values(self):
        assert DefaultTabType.Geography.value == "geography"


class TestRawEffectSize:
    """Test suite for RawEffectSize class"""

    def setup_method(self):
        """Setup for each test"""
        self.tags_with_level = [TagWithLevel(tag_label="education", level=1)]
        self.raw_effect_size = RawEffectSize(
            paper_id="123",
            paper_combined_id="T123",
            title="Test Paper",
            year=2023,
            doi_url="https://doi.org/test",
            doi="test",
            authors="Test Author",
            first_author="Test",
            journal_name="Test Journal",
            country_code="US",
            country_name="United States",
            region="North America",
            income_group="High Income",
            quality_score=8.5,
            quality_score_category="High",
            treatment_arm="Treatment A",
            intervention_id="INT1",
            intervention_tag_ids="1,2,3",
            intervention_tags_with_levels=self.tags_with_level,
            outcome_tags_with_levels=self.tags_with_level,
            intervention_tag_labels="Education,Training",
            outcome_tag_labels="Score,Performance",
            cohen_d=0.5,
            hedges_d=0.45,
            standardized_ci_lower=0.2,
            standardized_ci_upper=0.7,
        )

    def test_raw_effect_size_creation(self):
        assert self.raw_effect_size.paper_id == "123"
        assert self.raw_effect_size.paper_combined_id == "T123"
        assert self.raw_effect_size.title == "Test Paper"
        assert self.raw_effect_size.year == 2023
        assert self.raw_effect_size.doi_url == "https://doi.org/test"
        assert self.raw_effect_size.doi == "test"
        assert self.raw_effect_size.authors == "Test Author"
        assert self.raw_effect_size.first_author == "Test"
        assert self.raw_effect_size.journal_name == "Test Journal"
        assert self.raw_effect_size.country_code == "US"
        assert self.raw_effect_size.country_name == "United States"
        assert self.raw_effect_size.region == "North America"
        assert self.raw_effect_size.income_group == "High Income"
        assert self.raw_effect_size.quality_score == 8.5
        assert self.raw_effect_size.quality_score_category == "High"
        assert self.raw_effect_size.treatment_arm == "Treatment A"
        assert self.raw_effect_size.intervention_id == "INT1"
        assert self.raw_effect_size.intervention_tag_ids == "1,2,3"
        assert (
            self.raw_effect_size.intervention_tags_with_levels == self.tags_with_level
        )
        assert self.raw_effect_size.outcome_tags_with_levels == self.tags_with_level
        assert self.raw_effect_size.intervention_tag_labels == "Education,Training"
        assert self.raw_effect_size.outcome_tag_labels == "Score,Performance"
        assert self.raw_effect_size.cohen_d == 0.5
        assert self.raw_effect_size.hedges_d == 0.45
        assert self.raw_effect_size.standardized_ci_lower == 0.2
        assert self.raw_effect_size.standardized_ci_upper == 0.7


class TestRawIntervention:
    """Test suite for RawIntervention class"""

    def setup_method(self):
        """Setup for each test"""
        self.tags_with_level = [TagWithLevel(tag_label="education", level=1)]
        self.raw_intervention = RawIntervention(
            intervention_id=1,
            intervention_tag_ids="1,2,3",
            intervention_tag_labels="Education,Training",
            intervention_tag_short_labels="Edu,Train",
            intervention_tag_definitions="Definition 1,Definition 2",
            intervention_target_populations="Students",
            intervention_sectors="Education",
            intervention_objective="Improve learning",
            intervention_scale="Large",
            intervention_intensity="High",
            intervention_fidelity="Good",
            intervention_description="Test intervention",
            intervention_analysis_unit="Individual",
            intervention_cost="Low",
            intervention_tags_with_levels=self.tags_with_level,
        )

    def test_raw_intervention_creation(self):
        assert self.raw_intervention.intervention_id == 1
        assert self.raw_intervention.intervention_tag_ids == "1,2,3"
        assert self.raw_intervention.intervention_tag_labels == "Education,Training"
        assert self.raw_intervention.intervention_tag_short_labels == "Edu,Train"
        assert (
            self.raw_intervention.intervention_tag_definitions
            == "Definition 1,Definition 2"
        )
        assert self.raw_intervention.intervention_target_populations == "Students"
        assert self.raw_intervention.intervention_sectors == "Education"
        assert self.raw_intervention.intervention_objective == "Improve learning"
        assert self.raw_intervention.intervention_scale == "Large"
        assert self.raw_intervention.intervention_intensity == "High"
        assert self.raw_intervention.intervention_fidelity == "Good"
        assert self.raw_intervention.intervention_description == "Test intervention"
        assert self.raw_intervention.intervention_analysis_unit == "Individual"
        assert self.raw_intervention.intervention_cost == "Low"
        assert (
            self.raw_intervention.intervention_tags_with_levels == self.tags_with_level
        )


class TestRawOutcome:
    """Test suite for RawOutcome class"""

    def setup_method(self):
        """Setup for each test"""
        self.tags_with_level = [TagWithLevel(tag_label="score", level=1)]
        self.raw_outcome = RawOutcome(
            outcome_ids="1,2,3",
            outcome_tag_ids="4,5,6",
            outcome_tag_labels="Score,Performance",
            outcome_tag_short_labels="Scr,Perf",
            outcome_tag_definition="Test definition",
            outcome_target_populations="Students",
            outcome_sectors="Education",
            outcome_description="Test outcome",
            outcome_analysis_unit="Individual",
            outcome_connotation="Positive",
            outcome_type="Continuous",
            outcome_tags_with_levels=self.tags_with_level,
        )

    def test_raw_outcome_creation(self):
        assert self.raw_outcome.outcome_ids == "1,2,3"
        assert self.raw_outcome.outcome_tag_ids == "4,5,6"
        assert self.raw_outcome.outcome_tag_labels == "Score,Performance"
        assert self.raw_outcome.outcome_tag_short_labels == "Scr,Perf"
        assert self.raw_outcome.outcome_tag_definition == "Test definition"
        assert self.raw_outcome.outcome_target_populations == "Students"
        assert self.raw_outcome.outcome_sectors == "Education"
        assert self.raw_outcome.outcome_description == "Test outcome"
        assert self.raw_outcome.outcome_analysis_unit == "Individual"
        assert self.raw_outcome.outcome_connotation == "Positive"
        assert self.raw_outcome.outcome_type == "Continuous"
        assert self.raw_outcome.outcome_tags_with_levels == self.tags_with_level


class TestPlotData:
    """Test suite for PlotData class"""

    def setup_method(self):
        """Setup for each test"""
        self.score = Score(lower=0.1, upper=0.9, value=0.5)
        self.study = Study(id="123", label="Test Study", pulication_year=2023)
        self.raw_intervention = RawIntervention(intervention_id=1)
        self.raw_outcome = RawOutcome(outcome_ids="1")
        self.raw_effect_size = RawEffectSize(paper_id="123", paper_combined_id="T123")
        self.effect_size_data_point = EffectSizeDataPoint(
            label="Test Effect",
            score=self.score,
            country="USA",
            paper_id=123,
            paper_title="Test Paper",
            paper_citation="Author (2023)",
        )
        self.effect_size = EffectSize(
            data=[self.effect_size_data_point],
            outcome="Education",
            outcome_id=1,
            intervention="Training",
            intervention_id=2,
            aggregate=self.score,
        )
        self.plot_data = PlotData(
            default_tab=DefaultTabType.Geography,
            studies=[self.study],
            interventions=[self.raw_intervention],
            outcomes=[self.raw_outcome],
            flat_effect_sizes=[self.raw_effect_size],
            effect_sizes=[self.effect_size],
        )

    def test_plot_data_creation(self):
        assert self.plot_data.default_tab == DefaultTabType.Geography
        assert len(self.plot_data.studies) == 1
        assert len(self.plot_data.interventions) == 1
        assert len(self.plot_data.outcomes) == 1
        assert len(self.plot_data.flat_effect_sizes) == 1
        assert len(self.plot_data.effect_sizes) == 1


class TestPlotType:
    """Test suite for PlotType enum"""

    def test_plot_type_values(self):
        assert PlotType.DescriptivePlot.value == "descriptive_plot"


class TestPlot:
    """Test suite for Plot class"""

    def setup_method(self):
        """Setup for each test"""
        self.score = Score(lower=0.1, upper=0.9, value=0.5)
        self.study = Study(id="123", label="Test Study", pulication_year=2023)
        self.raw_intervention = RawIntervention(intervention_id=1)
        self.raw_outcome = RawOutcome(outcome_ids="1")
        self.raw_effect_size = RawEffectSize(paper_id="123", paper_combined_id="T123")
        self.effect_size_data_point = EffectSizeDataPoint(
            label="Test Effect",
            score=self.score,
            country="USA",
            paper_id=123,
            paper_title="Test Paper",
            paper_citation="Author (2023)",
        )
        self.effect_size = EffectSize(
            data=[self.effect_size_data_point],
            outcome="Education",
            outcome_id=1,
            intervention="Training",
            intervention_id=2,
            aggregate=self.score,
        )
        self.plot_data = PlotData(
            default_tab=DefaultTabType.Geography,
            studies=[self.study],
            interventions=[self.raw_intervention],
            outcomes=[self.raw_outcome],
            flat_effect_sizes=[self.raw_effect_size],
            effect_sizes=[self.effect_size],
        )
        self.plot = Plot(type=PlotType.DescriptivePlot, data=self.plot_data)

    def test_plot_creation(self):
        assert self.plot.type == PlotType.DescriptivePlot
        assert self.plot.data == self.plot_data
