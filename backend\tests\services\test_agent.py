import pytest
from unittest.mock import patch, AsyncMock
import uuid
import json
from services.agent import AgentService
from models.agent import AgentResponse

from utils.filesystem import get_file_path


def load_mock_data():
    file_path = get_file_path("mock-agent-responses.json")
    with open(file_path, "r") as f:
        return json.load(f)


MOCK_DATA = load_mock_data()


@pytest.fixture
def sample_conversation_id():
    return uuid.UUID("12345678-1234-5678-1234-************")


@pytest.fixture
def sample_query():
    return MOCK_DATA[0]["response"]["context"]["query"]


@pytest.fixture
def mock_response_data():
    return MOCK_DATA[0]


@pytest.fixture
def agent_service(sample_conversation_id, sample_query):
    return AgentService(sample_conversation_id, sample_query)


@pytest.mark.asyncio
async def test_execute_cached_response(agent_service, mock_response_data):
    # Mock the hashed_responses
    with patch(
        "services.agent.hashed_responses", {agent_service.query: mock_response_data}
    ):
        response = await agent_service.execute()

        assert isinstance(response, AgentResponse)
        assert response.context.query == agent_service.query


@pytest.mark.asyncio
async def test_execute_api_call(agent_service, mock_response_data):
    # Mock the API call when response is not cached
    with patch("services.agent.hashed_responses", {}), patch(
        "services.agent.post_json", return_value=mock_response_data
    ):

        response = await agent_service.execute()

        assert isinstance(response, AgentResponse)
        assert response.context.query == agent_service.query


@pytest.mark.asyncio
async def test_execute_api_error(agent_service):
    # Mock an API error
    with patch("services.agent.hashed_responses", {}), patch(
        "services.agent.post_json", side_effect=Exception("API Error")
    ):

        with pytest.raises(Exception) as exc_info:
            await agent_service.execute()

        assert str(exc_info.value) == "API Error"


@pytest.mark.asyncio
async def test_execute_invalid_response(agent_service):
    invalid_response = {"response": {"invalid": "data"}}

    # Mock an invalid API response
    with patch("services.agent.hashed_responses", {}), patch(
        "services.agent.post_json", return_value=invalid_response
    ):

        with pytest.raises(Exception):
            await agent_service.execute()


def test_agent_service_initialization(sample_conversation_id, sample_query):
    service = AgentService(sample_conversation_id, sample_query)

    assert service.conversation_id == sample_conversation_id
    assert service.query == sample_query
    assert service.agent_api_url == "https://agent.impact-ai-dev.app"


@pytest.mark.asyncio
async def test_execute_correct_api_call(agent_service, mock_response_data):
    # Mock the API call and verify the correct URL and data is used
    with patch("services.agent.hashed_responses", {}), patch(
        "services.agent.post_json", new=AsyncMock()
    ) as mock_post:

        mock_post.return_value = mock_response_data
        await agent_service.execute()

        expected_url = f"{agent_service.agent_api_url}/execute"
        expected_data = {
            "conversation_id": str(agent_service.conversation_id),
            "query": agent_service.query,
        }

        mock_post.assert_called_once_with(expected_url, body=expected_data, timeout=300)
