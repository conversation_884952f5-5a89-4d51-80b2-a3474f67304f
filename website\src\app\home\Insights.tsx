import React, { useState, useEffect, useRef } from "react";
import { Paper, Box, Grid, Typography, Card, CardContent } from "@mui/material";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";

interface Feature {
    number: number;
    label: string;
}

const useCounterAnimation = (endValue: number, duration: number = 2000, isVisible: boolean): number => {
    const [count, setCount] = useState<number>(0);

    useEffect(() => {
        if (isVisible) {
            let startTime: number;
            const step = (timestamp: number) => {
                if (!startTime) startTime = timestamp;
                const progress = Math.min((timestamp - startTime) / duration, 1);
                setCount(Math.floor(progress * endValue));
                if (progress < 1) requestAnimationFrame(step);
            };
            requestAnimationFrame(step);
        } else {
            setCount(0);
        }
    }, [endValue, duration, isVisible]);

    return count;
};

interface AnimatedCounterCardProps {
    feature: Feature;
    isVisible: boolean;
    isMobile: boolean;
    isTablet: boolean;
}

const AnimatedCounterCard: React.FC<AnimatedCounterCardProps> = ({ feature, isVisible, isMobile, isTablet }) => {
    const animatedNumber = useCounterAnimation(feature.number, 2000, isVisible);

    return (
        <Grid item xs={12} sm={4} md={4} sx={{ p: '0px !important' }}>
            <Card
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    borderRadius: "16px",
                    p: 2,
                    boxShadow: 0,
                    height: "auto",
                    background: "none",
                }}
            >
                <CardContent sx={{ textAlign: "center", width: "80%" }}>
                    <Typography variant={isMobile ? "h4" : isTablet ? "h3" : "h3"}>
                        {animatedNumber.toLocaleString()}+
                    </Typography>
                    <Typography variant="caption" sx={{ textTransform: "uppercase", mt: 1 }}>
                        {feature.label}
                    </Typography>
                </CardContent>
            </Card>
        </Grid>
    );
};

const Insights: React.FC = () => {
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const features: Feature[] = [
        { number: 7000, label: "RANDOMIZED CONTROLLED TRIALS" },
        { number: 25000, label: "INTERVENTIONS AND OUTCOMES" },
        { number: 140, label: "COUNTRIES" },
    ];

    const [isVisible, setIsVisible] = useState(false);
    const counterSectionRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                setIsVisible(entry.isIntersecting);
            },
            { threshold: 0.1 }
        );

        if (counterSectionRef.current) {
            observer.observe(counterSectionRef.current);
        }

        return () => {
            if (counterSectionRef.current) {
                observer.unobserve(counterSectionRef.current);
            }
        };
    }, []);

    return (
        <Paper
            ref={counterSectionRef}
            sx={{
                padding: "0px",
                width: "100%",
                height: "auto",
                background: "none",
                boxShadow: 0,
                borderRadius: "8px",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: isMobile ? "24px" : isTablet ? "40px" : "70px",
            }}
        >
            {/* Header Section */}
            <Box sx={{ textAlign: "center", width: "100%" }}>
                <Typography variant={isMobile ? "h3" : isTablet ? "h2" : "h2"}>
                    Quantified insights to maximize impact.
                </Typography>
            </Box>

            {/* Cards Section */}
            <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", width: "100%", mt: 0 }}>
                <Grid container spacing={3} justifyContent="center">
                    {features.map((feature, index) => (
                        <AnimatedCounterCard key={index} feature={feature} isVisible={isVisible} isMobile={isMobile} isTablet={isTablet} />
                    ))}
                </Grid>
            </Box>
        </Paper>
    );
};

export default Insights;