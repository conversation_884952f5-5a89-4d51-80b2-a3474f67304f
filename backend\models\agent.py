from dataclasses import dataclass, field
from dataclasses_json import dataclass_json
from typing import List, Optional
from collections import OrderedDict

import re
import random
from models.plot import (
    Study as PlotStudy,
    Plot,
    Score,
    PlotType,
    EffectSizeDataPoint,
    DefaultTabType,
    PlotData,
    TagWithLevel,
    EffectSize,
    RawOutcome,
    RawEffectSize,
    RawIntervention,
)


@dataclass_json
@dataclass
class RawDataUsed:
    paper_id: str
    paper_combined_id: str
    title: Optional[str] = None
    year: Optional[int] = None
    doi_url: Optional[str] = None
    doi: Optional[str] = None
    authors: Optional[str] = None
    first_author: Optional[str] = None
    journal_name: Optional[str] = None
    country_code: Optional[str] = None
    country_name: Optional[str] = None
    region: Optional[str] = None
    income_group: Optional[str] = None
    quality_score: Optional[float] = None
    quality_score_category: Optional[str] = None
    treatment_arm: Optional[str] = None
    intervention_id: Optional[str] = None
    intervention_tag_ids: Optional[str] = None
    intervention_tag_labels: Optional[str] = None
    intervention_tag_short_labels: Optional[str] = None
    intervention_tag_definitions: Optional[str] = None
    intervention_target_populations: Optional[str] = None
    intervention_sectors: Optional[str] = None
    intervention_objective: Optional[str] = None
    intervention_scale: Optional[str] = None
    intervention_intensity: Optional[str] = None
    intervention_fidelity: Optional[str] = None
    intervention_description: Optional[str] = None
    intervention_analysis_unit: Optional[str] = None
    intervention_start_date: Optional[str] = None
    intervention_end_date: Optional[str] = None
    intervention_cost: Optional[str] = None
    attrition_lower: Optional[float] = None
    attrition_upper: Optional[float] = None
    compliance_rate_lower: Optional[float] = None
    compliance_rate_upper: Optional[float] = None
    intervention_labels: Optional[str] = None
    intervention_ids: Optional[str] = None
    outcome_ids: Optional[str] = None
    outcome_tag_ids: Optional[str] = None
    outcome_tag_labels: Optional[str] = None
    outcome_tag_short_labels: Optional[str] = None
    outcome_tag_definition: Optional[str] = None
    outcome_target_populations: Optional[str] = None
    outcome_sectors: Optional[str] = None
    outcome_description: Optional[str] = None
    outcome_analysis_unit: Optional[str] = None
    outcome_connotation: Optional[str] = None
    outcome_type: Optional[str] = None
    outcome_labels: Optional[str] = None
    is_primary_period: Optional[int] = None
    data_collection_round: Optional[str] = None
    cohen_d: Optional[float] = None
    hedges_d: Optional[float] = None
    standardized_ci_lower: Optional[float] = None
    standardized_ci_upper: Optional[float] = None

    @property
    def intervention_tags_with_levels(self) -> List[TagWithLevel]:
        return self._to_tag_levels(self.intervention_tag_labels)

    @property
    def outcome_tags_with_levels(self) -> List[TagWithLevel]:
        return self._to_tag_levels(self.outcome_tag_labels)

    @property
    def income_group_code(self) -> Optional[str]:
        if not self.income_group:
            return None

        words = [word for word in self.income_group.split()]
        return "".join(word[0].upper() for word in words)

    @property
    def region_code(self) -> Optional[str]:
        if not self.region:
            return None

        words = [word for word in self.region.split()]
        return "".join(word[0].upper() for word in words)

    @property
    def citation(self) -> str:
        return f"{self.authors} ({self.year})"

    @property
    def parsed_intervention_tag_ids(self) -> Optional[List[int]]:
        if not self.intervention_tag_ids:
            return None
        return [
            int(id.strip()) for id in self.intervention_tag_ids.split(",") if id.strip()
        ]

    @property
    def parsed_intervention_ids(self) -> Optional[List[int]]:
        if not self.intervention_ids:
            return None
        return [
            int(id.strip()) for id in self.intervention_ids.split(",") if id.strip()
        ]

    @property
    def parsed_outcome_tag_ids(self) -> Optional[List[int]]:
        if not self.outcome_tag_ids:
            return None
        return [int(id.strip()) for id in self.outcome_tag_ids.split(",") if id.strip()]

    @property
    def parsed_outcome_ids(self) -> Optional[List[int]]:
        if not self.outcome_ids:
            return None
        return [int(id.strip()) for id in self.outcome_ids.split(",") if id.strip()]

    @property
    def score(self) -> dict:
        if self.cohen_d:
            return Score(
                lower=self.standardized_ci_lower,
                upper=self.standardized_ci_upper,
                value=self.cohen_d,
            )
        return Score(
            lower=self.standardized_ci_lower,
            upper=self.standardized_ci_upper,
            value=self.hedges_d,
        )

    def _to_tag_levels(self, tags: str) -> List[TagWithLevel]:
        if not tags:
            return []

        tag_with_levels = []
        for idx, tag_label in enumerate(tags.split(":")):
            tag_with_levels.append(
                TagWithLevel(
                    tag_label=tag_label.strip(),
                    level=idx,
                )
            )
        return tag_with_levels

    def _extract_map(self, labels: str, ids: str) -> dict:
        """Extracts a dictionary mapping IDs to labels."""
        if not labels or not ids:
            return {}
        labels = labels.split(",")
        ids = str(ids).split(",")
        if len(labels) != len(ids):
            # TODO: Raise here so that we know what data isnt correct.
            return {}
        keys = {}
        for i in range(len(ids)):
            keys[ids[i]] = labels[i].strip()
        return keys

    def extracted_intervention_map(self) -> dict:
        """Extracts a dictionary mapping intervention IDs to labels."""

        if not self.intervention_tag_short_labels or not self.intervention_id:
            return {}

        return self._extract_map(
            self.intervention_tag_short_labels, self.intervention_id
        )

    def extracted_outcome_map(self) -> dict:
        """Extracts a dictionary mapping outcome IDs to labels."""
        if not self.outcome_tag_short_labels or not self.outcome_ids:
            return {}
        return self._extract_map(self.outcome_tag_short_labels, self.outcome_ids)

    def to_plot_study_format(self) -> PlotStudy:
        return PlotStudy(
            id=self.paper_id,
            label=self.title,
            citation=self.citation,
            pulication_year=self.year,
            first_author=self.first_author,
            population=random.randint(1000, 50000),  # TODO: remove this mocking
            country=self.country_name,
            region_code=self.region_code,
            region_label=self.region,
            quality_score=self.quality_score,
            quality_score_group=self.quality_score_category,
            income_group_code=self.income_group_code,
            income_group_label=self.income_group,
        )

    def to_source_format(self):
        return {
            "message_id": None,
            "paper_id": self.paper_id,
            "short_paper_id": self.paper_combined_id,
            "title": self.title,
            "doi_url": self.doi_url,
            "citation": self.citation,
            "journal_name": self.journal_name,
        }


@dataclass_json
@dataclass
class ToolData:
    data_used: List[RawDataUsed] = field(default_factory=list)

    @property
    def flat_effect_sizes(self) -> List[RawEffectSize]:
        l = []
        for data in self.data_used:
            l.append(
                RawEffectSize(
                    paper_id=data.paper_id,
                    paper_combined_id=data.paper_combined_id,
                    title=data.title,
                    year=data.year,
                    doi_url=data.doi_url,
                    doi=data.doi,
                    authors=data.authors,
                    first_author=data.first_author,
                    journal_name=data.journal_name,
                    country_code=data.country_code,
                    country_name=data.country_name,
                    region=data.region,
                    income_group=data.income_group,
                    quality_score=data.quality_score,
                    quality_score_category=data.quality_score_category,
                    treatment_arm=data.treatment_arm,
                    intervention_id=data.intervention_id,
                    intervention_tag_ids=data.intervention_tag_ids,
                    intervention_tags_with_levels=data.intervention_tags_with_levels,
                    intervention_tag_labels=data.intervention_tag_labels,
                    intervention_tag_short_labels=data.intervention_tag_short_labels,
                    intervention_tag_definitions=data.intervention_tag_definitions,
                    intervention_target_populations=data.intervention_target_populations,
                    intervention_sectors=data.intervention_sectors,
                    intervention_objective=data.intervention_objective,
                    intervention_scale=data.intervention_scale,
                    intervention_intensity=data.intervention_intensity,
                    intervention_fidelity=data.intervention_fidelity,
                    intervention_description=data.intervention_description,
                    intervention_analysis_unit=data.intervention_analysis_unit,
                    intervention_cost=data.intervention_cost,
                    outcome_ids=data.outcome_ids,
                    outcome_tag_ids=data.outcome_tag_ids,
                    outcome_tag_labels=data.outcome_tag_labels,
                    outcome_tags_with_levels=data.outcome_tags_with_levels,
                    outcome_tag_short_labels=data.outcome_tag_short_labels,
                    outcome_tag_definition=data.outcome_tag_definition,
                    outcome_target_populations=data.outcome_target_populations,
                    outcome_sectors=data.outcome_sectors,
                    outcome_description=data.outcome_description,
                    outcome_analysis_unit=data.outcome_analysis_unit,
                    outcome_connotation=data.outcome_connotation,
                    outcome_type=data.outcome_type,
                    is_primary_period=data.is_primary_period,
                    data_collection_round=data.data_collection_round,
                    cohen_d=data.cohen_d,
                    hedges_d=data.hedges_d,
                    standardized_ci_lower=data.standardized_ci_lower,
                    standardized_ci_upper=data.standardized_ci_upper,
                )
            )
        return l

    @property
    def interventions(self) -> List[RawIntervention]:
        l = {}
        for data in self.data_used:
            intervention_map = data.extracted_intervention_map()
            if not intervention_map:
                continue

            for (
                intervention_id,
                intervention_tag_short_labels,
            ) in intervention_map.items():
                if intervention_id not in l:
                    l[intervention_id] = RawIntervention(
                        intervention_id=intervention_id,
                        intervention_tag_ids=data.intervention_tag_ids,
                        intervention_tag_labels=data.intervention_tag_labels,
                        intervention_tags_with_levels=data.intervention_tags_with_levels,
                        intervention_tag_short_labels=intervention_tag_short_labels,
                        intervention_tag_definitions=data.intervention_tag_definitions,
                        intervention_target_populations=data.intervention_target_populations,
                        intervention_sectors=data.intervention_sectors,
                        intervention_objective=data.intervention_objective,
                        intervention_scale=data.intervention_scale,
                        intervention_intensity=data.intervention_intensity,
                        intervention_fidelity=data.intervention_fidelity,
                        intervention_description=data.intervention_description,
                        intervention_analysis_unit=data.intervention_analysis_unit,
                        intervention_cost=data.intervention_cost,
                    )

        return list(l.values())

    @property
    def outcomes(self) -> List[RawOutcome]:
        l = {}
        for data in self.data_used:
            outcome_map = data.extracted_outcome_map()
            if not outcome_map:
                continue

            for outcome_id, outcome_tag_short_labels in outcome_map.items():
                if outcome_id not in l:
                    l[outcome_id] = RawOutcome(
                        outcome_ids=outcome_id,
                        outcome_tag_ids=data.outcome_tag_ids,
                        outcome_tag_labels=data.outcome_tag_labels,
                        outcome_tags_with_levels=data.outcome_tags_with_levels,
                        outcome_tag_short_labels=outcome_tag_short_labels,
                        outcome_tag_definition=data.outcome_tag_definition,
                        outcome_target_populations=data.outcome_target_populations,
                        outcome_sectors=data.outcome_sectors,
                        outcome_description=data.outcome_description,
                        outcome_analysis_unit=data.outcome_analysis_unit,
                        outcome_connotation=data.outcome_connotation,
                        outcome_type=data.outcome_type,
                    )
        return list(l.values())

    @property
    def unique_studies(self) -> List[RawDataUsed]:
        """Returns unique RawDataUsed entries from data_used."""
        result = {}
        for data in self.data_used:
            if data.paper_id in result:
                continue
            result[data.paper_id] = data
        return result.values()

    @property
    def sources(self) -> List[PlotStudy]:
        """Convert RawDataUsed entries into Source objects."""

        all = []
        for study in self.unique_studies:
            all.append(study.to_source_format())
        return all

    @property
    def plot_studies(self) -> List[PlotStudy]:
        """Convert RawDataUsed entries into Plot Study objects."""
        all = []
        for study in self.unique_studies:
            all.append(study.to_plot_study_format())
        return all

    @property
    def plot_study_effect_sizes(self) -> List[EffectSize]:
        """Convert RawDataUsed entries into Plot Study Finding objects."""
        pair_hash = {}
        for data in self.data_used:
            intervention_map = data.extracted_intervention_map()
            outcome_map = data.extracted_outcome_map()
            if not intervention_map or not outcome_map:
                continue

            for intervention_id, intervention_label in intervention_map.items():
                # intervention_label = "behavioral intervention:cognitive behavioral therapy for dysmenorrhea"
                # short_intervention_label = "behavioral intervention"
                # short_intervention_label = (
                #     intervention_label.split(":")[0]
                #     if intervention_label
                #     else intervention_label
                # )

                for outcome_id, outcome_label in outcome_map.items():

                    # outcome_label = "knowledge:dysmenorrhea"
                    # short_outcome_label = "knowledge"
                    # short_outcome_label = (
                    #     outcome_label.split(":")[0] if outcome_label else outcome_label
                    # )

                    key = f"{outcome_label}_{intervention_label}"
                    if key not in pair_hash:
                        pair_hash[key] = {
                            "outcome_id": outcome_id,
                            "outcome_label": outcome_label,
                            "intervention_id": intervention_id,
                            "intervention_label": intervention_label,
                            "data_points": [data],
                        }
                        continue
                    pair_hash[key]["data_points"].append(data)
        effect_sizes = []
        for pair_obj in pair_hash.values():
            data_points = []
            for dp in pair_obj["data_points"]:
                data_point = EffectSizeDataPoint(
                    label=dp.title,
                    score=dp.score,
                    country=dp.country_name,
                    paper_id=dp.paper_id,
                    paper_title=dp.title,
                    paper_citation=dp.citation,
                )
                data_points.append(data_point)

            aggregate = Score(
                lower=round(
                    sum(dp.score.lower for dp in data_points) / len(data_points), 3
                ),
                upper=round(
                    sum(dp.score.upper for dp in data_points) / len(data_points), 3
                ),
                value=round(
                    sum(dp.score.value for dp in data_points) / len(data_points), 3
                ),
            )

            effect_sizes.append(
                EffectSize(
                    data=data_points,
                    outcome_id=pair_obj["outcome_id"],
                    outcome=pair_obj["outcome_label"],
                    intervention_id=pair_obj["intervention_id"],
                    intervention=pair_obj["intervention_label"],
                    aggregate=aggregate,
                )
            )
        return effect_sizes


@dataclass_json
@dataclass
class ResponseContext:
    query: str
    conversation_id: str
    tool_data: ToolData


@dataclass_json
@dataclass
class AgentResponse:
    response: str
    context: ResponseContext

    @property
    def summary_sources(self) -> list[str]:
        _, sources = self.__extract_clean_summary()
        return sources

    @property
    def formated_summary(self) -> str:
        summary, sources = self.__extract_clean_summary()
        unique_sources = ",".join(sources)
        return summary + f" [{unique_sources}]"

    def has_plot_data(self):
        return len(self.context.tool_data.data_used) > 0

    def has_sources(self):
        return len(self.context.tool_data.data_used) > 0

    def sources_data(self):
        return self.__sources_ordered_by_summary_sources_priority()

    def plot_data(self):
        studies = self.context.tool_data.plot_studies
        effect_sizes = self.context.tool_data.plot_study_effect_sizes
        flat_effect_sizes = self.context.tool_data.flat_effect_sizes
        outcomes = self.context.tool_data.outcomes
        interventions = self.context.tool_data.interventions
        return Plot(
            type=PlotType.DescriptivePlot,
            data=PlotData(
                default_tab=DefaultTabType.Geography,
                studies=studies,
                effect_sizes=effect_sizes,
                outcomes=outcomes,
                interventions=interventions,
                flat_effect_sizes=flat_effect_sizes,
            ),
        )

    def __sources_ordered_by_summary_sources_priority(self):
        summary_sources = self.summary_sources
        full_sources = self.context.tool_data.sources

        priority_papers = []
        other_papers = []

        for source in full_sources:
            short_paper_id = source["short_paper_id"]
            if short_paper_id in summary_sources:
                priority_papers.append(source)
            else:
                other_papers.append(source)

        source_order = {
            short_paper_id: index
            for index, short_paper_id in enumerate(summary_sources)
        }
        priority_papers.sort(
            key=lambda source: source_order.get(
                source.get("short_paper_id"), float("inf")
            )
        )

        return priority_papers + other_papers

    def __extract_clean_summary(self):
        response_text = self.response

        paper_ids_in_data = []
        try:
            for item in self.context.tool_data.data_used:
                paper_ids_in_data.append(item.paper_combined_id)
        except (KeyError, TypeError, AttributeError):
            pass  # If any key is missing, just continue with an empty list

        ordered_paper_ids = OrderedDict()

        unified_pattern = r"\[((?:[A-Z]\d+(?:,\s*[A-Z]\d+)*))\]"
        unified_matches = re.findall(unified_pattern, response_text)
        for match in unified_matches:
            ids = [paper_id.strip() for paper_id in match.split(",")]
            for paper_id in ids:
                ordered_paper_ids[paper_id] = None

        cleaned_text = re.sub(unified_pattern, "", response_text)

        validated_paper_ids = []
        invalid_paper_ids = []
        for paper_id in ordered_paper_ids.keys():
            if paper_id in paper_ids_in_data:
                validated_paper_ids.append(paper_id)
            else:
                invalid_paper_ids.append(paper_id)

        return cleaned_text, validated_paper_ids
