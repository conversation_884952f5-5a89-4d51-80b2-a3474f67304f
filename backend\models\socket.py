from enum import StrEnum, auto
import json
from dataclasses_json import dataclass_json
from dataclasses import dataclass, field
from fastapi import WebSocket
from typing import Optional, Union


class Action(StrEnum):
    Auth = auto()
    Subscribe = auto()
    Unsubscribe = auto()
    Send = auto()


class SendType(StrEnum):
    Message = auto()


class AuthType(StrEnum):
    User = auto()
    Application = auto()


class SubscriptionType(StrEnum):
    Conversation = auto()
    Message = auto()


@dataclass_json
@dataclass
class AuthData:
    type: AuthType
    id: str
    token: str


@dataclass_json
@dataclass
class SubscriptionData:
    type: SubscriptionType
    id: str


@dataclass_json
@dataclass
class SendData:
    type: SendType
    subscription_id: str
    content: str


@dataclass_json
@dataclass
class Response:
    status: str
    message: str


@dataclass_json
@dataclass
class Message:
    action: Action
    data: Union[AuthData, SubscriptionData, SendData]

    def action_is_auth(self) -> bool:
        return self.action == Action.Auth

    def action_is_subscribe(self) -> bool:
        return self.action == Action.Subscribe

    def action_is_unsubscribe(self) -> bool:
        return self.action == Action.Unsubscribe

    def action_is_send(self) -> bool:
        return self.action == Action.Send


@dataclass
class ActiveConnection:
    socket: WebSocket
    type: Optional[AuthType] = None
    id: Optional[str] = None
    subscribed_to: list[SubscriptionData] = field(default_factory=list)

    async def receive_message(self) -> Message:
        data = await self.socket.receive_text()
        return Message.from_json(data)

    async def send_error(self, message: str):
        await self.send_text(Response(status="error", message=message))

    async def send_warning(self, message: str):
        await self.send_text(Response(status="warning", message=message))

    async def send_success(self, message: str):
        await self.send_text(Response(status="success", message=message))

    async def send_text(self, data: Response):
        await self.socket.send_text(json.dumps(data.to_dict()))

    def is_authenticated(self) -> bool:
        return self.type is not None

    def type_user(self) -> bool:
        return self.type == AuthType.User if self.type else False

    def type_application(self) -> bool:
        return self.type == AuthType.Application if self.type else False

    def update_auth(self, data: AuthData):
        self.type = data.type
        self.id = data.id

    def is_subscribed(self, data: SubscriptionData) -> bool:
        return any(
            sub.id == data.id and sub.type == data.type for sub in self.subscribed_to
        )

    def subscribe(self, data: SubscriptionData):
        if not self.is_subscribed(data):
            self.subscribed_to.append(data)

    def unsubscribe(self, data: SubscriptionData):
        self.subscribed_to = [
            sub
            for sub in self.subscribed_to
            if sub.id != data.id or sub.type != data.type
        ]
