"use server";

type WaitlistFormData = {
  email: string;
  organization: string;
};

const RECAPTCHA_SITE_KEY = "6Lf65worAAAAAD1HP-QqUYI8cZP5pG0ZR85haRwU"

const isProdEnvironment = process.env.TARGET === "production"

const backendAPIURL = isProdEnvironment ? "https://api.impact-ai-prod.app/waitlist": "https://api.impact-ai-dev.app/waitlist"

export async function submitWaitlist(formData: WaitlistFormData, recaptchaToken: string) {
  const assessmentRes = await fetch(`https://recaptchaenterprise.googleapis.com/v1/projects/impactai-430615/assessments?key=${process.env.RECAPTCHA_API_KEY}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      event: {
        token: recaptchaToken,
        siteKey: RECAPTCHA_SITE_KEY,
        expectedAction: "submit",
      },
    }),
  })
  const assessmentData = await assessmentRes.json();
  if (!assessmentData.tokenProperties?.valid) {
    return { success: false };
  }

  const res = await fetch(backendAPIURL, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(formData),
  });
  const data = await res.json();
  return data
}
