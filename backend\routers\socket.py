import structlog
import asyncio
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
import uuid
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from services.socket import SocketService

router = APIRouter()

logger = structlog.get_logger()

socket_service = SocketService()


@router.websocket("/")
async def websocket_endpoint(websocket: WebSocket):
    connection_id = str(uuid.uuid4())
    logger.info("New WebSocket connection", connection_id=connection_id)
    loop = asyncio.get_running_loop()
    with ThreadPoolExecutor(max_workers=4) as executor:
        try:
            await socket_service.connect(connection_id, websocket)
            while True:
                await loop.run_in_executor(
                    executor,
                    lambda: asyncio.run(socket_service.on_message(connection_id)),
                )
                await asyncio.sleep(1)
        except WebSocketDisconnect:
            logger.info("WebSocket disconnected", connection_id=connection_id)
            await socket_service.disconnect(connection_id)
        except Exception as e:
            logger.error("WebSocket error", error=str(e), connection_id=connection_id)
            await socket_service.disconnect(connection_id)
