import json
import logging
import asyncio
import websockets
from typing import Any, Optional, Callable

logging.basicConfig(level=logging.INFO)

AGENT_SOCKET_ID = "agent"
AGENT_SOCKET_SECRET = "tT6DeJcuXCrBkDak4yFziFmCP8"
WEBSOCKET_URI = "wss://api.impact-ai-dev.app"


class WebSocketConnectionManager:
    def __init__(
        self,
        uri: str,
        authentication_callback: Optional[Callable[[], Any]] = None,
        max_retry_attempts: int = 5,
        retry_delay: float = 2.0,
    ):
        self.uri = uri
        self.websocket: Optional[websockets.WebSocketClientProtocol] = None
        self.connected = False
        self.authentication_callback = authentication_callback
        self.max_retry_attempts = max_retry_attempts
        self.retry_delay = retry_delay
        self.logger = logging.getLogger(self.__class__.__name__)

    async def connect(self) -> bool:
        for attempt in range(self.max_retry_attempts):
            try:
                self.websocket = await websockets.connect(self.uri)
                self.connected = True
                self.logger.info(f"Connected to {self.uri}")

                if self.authentication_callback:
                    await self.authentication_callback()

                return True
            except Exception as e:
                self.connected = False
                self.logger.warning(f"Connection attempt {attempt + 1} failed: {e}")

                await asyncio.sleep(self.retry_delay * (2**attempt))

        self.logger.error("Failed to establish WebSocket connection after max attempts")
        return False

    async def send(self, message: Any) -> bool:
        if not self.connected or not self.websocket:
            self.logger.error("Cannot send: Not connected")
            return False

        try:
            msg = json.dumps(message) if isinstance(message, dict) else str(message)
            await self.websocket.send(msg)
            self.logger.debug(f"Sent: {msg}")
            return True
        except (websockets.ConnectionClosed, Exception) as e:
            self.logger.error(f"Send failed: {e}")
            self.connected = False
            return False

    async def receive(self) -> Optional[str]:
        if not self.connected or not self.websocket:
            return None

        try:
            message = await self.websocket.recv()
            self.logger.debug(f"Received: {message}")
            return message
        except websockets.ConnectionClosed:
            self.logger.warning("Connection closed during receive")
            self.connected = False
            return None
        except Exception as e:
            self.logger.error(f"Receive error: {e}")
            self.connected = False
            return None

    async def close(self):
        if self.websocket:
            await self.websocket.close()
            self.connected = False


class SocketService:
    def __init__(self, socket_uri: str, socket_id: str, socket_secret: str):
        self.socket_id = socket_id
        self.socket_secret = socket_secret

        self.connection_manager = WebSocketConnectionManager(
            uri=socket_uri, authentication_callback=self._authenticate
        )

    async def ensure_connected(self):
        while not self.connection_manager.connected:
            if not await self.connection_manager.connect():
                await asyncio.sleep(2)

    async def _authenticate(self):
        auth_data = {
            "action": "auth",
            "data": {
                "token": self.socket_secret,
                "id": self.socket_id,
                "type": "application",
            },
        }
        await self.connection_manager.send(auth_data)

    async def send_message(self, subscription_id: str, message: str):
        # await self.ensure_connected()
        # message_object = {
        #     "action": "send",
        #     "data": {
        #         "type": "message",
        #         "subscription_id": subscription_id,
        #         "content": message,
        #     },
        # }
        # await self.connection_manager.send(message_object)
        pass

    async def start_message_listener(self, message_handler: Callable[[str], Any]):
        while True:
            await self.ensure_connected()

            message = await self.connection_manager.receive()
            if message:
                try:
                    await message_handler(message)
                except Exception as e:
                    logging.error(f"Error processing message: {e}")

            await asyncio.sleep(0.1)

    async def close(self):
        """Close the websocket connection."""
        if hasattr(self, "connection_manager"):
            await self.connection_manager.close()


socket_service = SocketService(
    socket_uri=WEBSOCKET_URI,
    socket_id=AGENT_SOCKET_ID,
    socket_secret=AGENT_SOCKET_SECRET,
)
