from pathlib import Path


def root_dir():
    """Get the directory path from the root of the project."""
    return Path(__file__).parent.parent.resolve()


def get_file_path(file_name: str) -> Path:
    """Get the file path from the root of the project."""
    return root_dir() / "files" / file_name


def get_prompt_file_path(file_name: str) -> Path:
    """Get the prompt file path from the root of the project."""
    return root_dir() / "prompts" / file_name
