import { ReactNode, useState, useRef, useEffect, useContext } from 'react';
import {
  Drawer,
  AppBar,
  Box,
  IconButton,
  Toolbar,
  Avatar,
  ListItemIcon,
  ListItemText,
  CssBaseline,
  Container,
  Menu,
  MenuItem
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import Sidebar from '../Sidebar/Sidebar';
import { useIsMobile, useIsTablet } from './MobileUtils';
import ErrorPage from '../Error/ErrorPage';
import './Layout.css';
import { useTheme } from '@mui/material/styles';
import EditNoteOutlinedIcon from '@mui/icons-material/EditNoteOutlined';
import { useNavigate } from 'react-router-dom';
import { UserData } from '../../types/AuthTypes';
import { logoutUser } from "../../services/authService";
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';
import LogoutOutlinedIcon from '@mui/icons-material/LogoutOutlined';
import { LayoutContext } from './LayoutContext';

interface RequestProps {
  children: ReactNode;
}

const Layout: React.FC<RequestProps> = ({ children }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const mainScrollRef = useRef<HTMLDivElement>(null);
  const isMobileOrTablet = isMobile || isTablet;
  const [userData, setUserData] = useState<UserData | null>(null);
  const [userFirstLetter, setUserFirstLetter] = useState<string | ''>('');
  const [appBarPaddingLeft, setAppBarPaddingLeft] = useState('48px');
  const [anchorElUser, setAnchorElUser] = useState<null | HTMLElement>(null);

  const {
    centeredContent,
    errorMessage,
    updateErrorMessageState,
    isSidebarCollapsed,
  } = useContext(LayoutContext);

  const drawerWidth = isSidebarCollapsed ? '64px' : '17vw';

  useEffect(() => {
    const savedUserData = localStorage.getItem("userData");
    if (savedUserData) {
      const userData = JSON.parse(savedUserData);
      setUserData(userData);
      const email = userData?.user?.email || '';
      const emailLocalPart = email.split('@')[0];
      const nameParts = emailLocalPart.split('.');

      let firstName = nameParts[0] || '';
      let lastName = nameParts[1] || '';

      if (!lastName) {
        lastName = '';
      }

      const userInitials = `${firstName.charAt(0).toUpperCase()}`;
      setUserFirstLetter(userInitials);
    }

    const getPlatformPadding = () => {
      const nav = navigator as Navigator & { userAgentData?: { platform?: string } };
      const platform = nav.userAgentData?.platform || navigator.userAgent;
      return /Windows/i.test(platform) ? '64px' : '48px';
    };
    setAppBarPaddingLeft(getPlatformPadding());
  }, []);

  const handleOpenUserMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElUser(event.currentTarget);
  };

  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };

  const handleLogout = () => {
    handleCloseUserMenu();
    logoutUser();
    navigate('/auth');
  };

  const handleSettings = () => {
    handleCloseUserMenu();
    navigate('/settings');
  };

  const handleDrawerToggle = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  const renderDrawer = () => (
    <Drawer
      variant={'persistent'}
      anchor="left"
      open={isMobileOrTablet ? isDrawerOpen : true}
      onClose={handleDrawerToggle}
      sx={{
        '& .MuiDrawer-paper': {
          width: isMobileOrTablet
            ? (isDrawerOpen ? '60vw' : '0px')
            : drawerWidth,
          boxSizing: 'border-box',
          p: '0px',
          border: '0px',
          background: theme.elevation.paperElevationTwo,
          transition: 'width 0.3s ease-out',
          overflowY: 'inherit !important',
          visibility: isMobileOrTablet && !isDrawerOpen ? 'hidden' : 'visible',
          minWidth: isMobileOrTablet && !isDrawerOpen ? '0px' : undefined,
        },
      }}
    >
      <Sidebar
        isMobileOrTablet={isMobileOrTablet}
        handleMobileDrawerClose={handleDrawerToggle}
      />
    </Drawer>
  );

  const renderAppBar = () => {
    return isMobileOrTablet ? (
      <AppBar
        position="static"
        sx={{
          boxShadow: "none",
          background: theme.palette.primary.main,
        }}
      >
        <Toolbar sx={{ display: "flex" }}>
          <IconButton
            aria-label="open drawer"
            onClick={handleDrawerToggle}
            sx={{ color: theme.palette.background.default, mx: -1 }}
          >
            <MenuIcon fontSize="medium" />
          </IconButton>

          <Box sx={{ flexGrow: 1, display: "flex", justifyContent: "center" }}></Box>

          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <IconButton
              aria-label="new chat"
              sx={{ color: theme.palette.background.default }}
              onClick={() => navigate("/")}
            >
              <EditNoteOutlinedIcon fontSize="medium" />
            </IconButton>
            <IconButton onClick={handleOpenUserMenu} sx={{ p: 0 }}>
              <Avatar
                sx={{
                  bgcolor: theme.palette.common.white,
                  color: theme.palette.primary.main,
                  width: 32,
                  height: 32,
                  fontSize: "16px",
                  lineHeight: "150%",
                  fontWeight: "normal",
                  cursor: "pointer",
                  letterSpacing: "0.15px",
                }}
              >
                {userFirstLetter}
              </Avatar>
            </IconButton>
            <Menu
              elevation={2}
              sx={{
                mt: '35px',
                width: 450,
                p: '0px',
                '& .MuiPaper-root': {
                  borderRadius: 3,
                  marginTop: theme.spacing(1),
                  minWidth: 180,
                  backgroundColor: `${theme.palette.background.default}`,
                  borderColor: `${theme.palette.secondary.main}`,
                  borderWidth: '2px',
                  padding: '0px',
                  color: `${theme.palette.text.secondary}`,
                  '& .MuiList-root': {
                    p: '8px'
                  },
                  '& .MuiMenuItem-root': {
                    borderRadius: '4px',
                    padding: '4px',
                    color: theme.palette.action.active,
                    '&:hover': {
                      background: `${theme.palette.primary.hover}`,
                    },
                    '&:active': {
                      background: `${theme.palette.primary.hover}`,
                    }
                  }
                }
              }}
              id="menu-appbar"
              anchorEl={anchorElUser}
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorElUser)}
              onClose={handleCloseUserMenu}
            >
              <MenuItem onClick={handleSettings}>
                <ListItemIcon>
                  <SettingsOutlinedIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Settings" />
              </MenuItem>
              <MenuItem onClick={handleLogout} >
                <ListItemIcon>
                  <LogoutOutlinedIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Logout" />
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>
    ) : (
      <AppBar
        position="fixed"
        sx={{
          boxShadow: "none",
          background: theme.palette.common.white,
          display: 'flex',
          flexDirection: 'row-reverse',
          justifyContent: 'flex-start',
          height: '82px',
          top: 0,
          left: 0,
          right: 0,
          my: '0px',
          py: '0px',
          pl: '48px',
          pr: appBarPaddingLeft,
          zIndex: 999,
          width: `calc(100% - ${drawerWidth})`,
          ml: drawerWidth,
          transition: 'margin-left 0.3s ease, width 0.3s ease',
        }}
      >
        <Toolbar sx={{
          display: "flex",
          p: '0px !important',
          m: '0px !important',
        }}>
          <IconButton onClick={handleOpenUserMenu} sx={{ p: 0 }}>
            <Avatar
              sx={{
                bgcolor: '#478FFC',
                color: theme.palette.common.white,
                width: 32,
                height: 32,
                fontSize: "16px",
                lineHeight: "150%",
                fontWeight: "normal",
                cursor: "pointer",
                letterSpacing: "0.15px",
                p: '0px !important',
                m: '0px !important',
              }}
            >
              {userFirstLetter}
            </Avatar>
          </IconButton>
          <Menu
            elevation={2}
            sx={{
              mt: '35px',
              width: 450,
              p: '0px',
              '& .MuiPaper-root': {
                borderRadius: 3,
                marginTop: theme.spacing(1),
                minWidth: 180,
                backgroundColor: `${theme.palette.background.default}`,
                borderColor: `${theme.palette.secondary.main}`,
                borderWidth: '2px',
                padding: '0px',
                color: `${theme.palette.text.secondary}`,
                '& .MuiList-root': {
                  p: '8px'
                },
                '& .MuiMenuItem-root': {
                  borderRadius: '4px',
                  padding: '4px',
                  color: theme.palette.action.active,
                  '&:hover': {
                    background: `${theme.palette.primary.hover}`,
                  },
                  '&:active': {
                    background: `${theme.palette.primary.hover}`,
                  }
                }
              }
            }}
            id="menu-appbar"
            anchorEl={anchorElUser}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorElUser)}
            onClose={handleCloseUserMenu}
          >
            <MenuItem onClick={handleSettings}>
              <ListItemIcon>
                <SettingsOutlinedIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Settings" />
            </MenuItem>
            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <LogoutOutlinedIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Logout" />
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>
    );
  };

  const renderMainContent = () => (
    <Box
      className="main-wrapper"
      ref={mainScrollRef}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: "calc(100vh - 82px)",
        flexGrow: 1,
        paddingTop: "82px",
        p: 0,
        ml: { sm: isMobileOrTablet ? 0 : drawerWidth },
        transition: 'margin-left 0.3s ease',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
      }}
    >
      {errorMessage ? (
        <ErrorPage
          errorMessage={errorMessage}
          error={errorMessage}
          onClose={() => updateErrorMessageState('')}
          isMobile={isMobile}
        />
      ) : (
        <Box
          id="main"
          sx={{
            width: '100%',
            height: centeredContent ? '100%' : `calc(100% - 14.4vh - 8vh)`,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'stretch',
            alignItems: 'stretch',
            flex: 1,
          }}
        >
          {children}
        </Box>
      )}
    </Box>
  );

  return (
    <>
      <CssBaseline enableColorScheme />
      <Container
        disableGutters
        maxWidth="100%"
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100vh',
          width: '100%',
          overflow: 'hidden'
        }}
      >
        {renderAppBar()}
        {renderDrawer()}
        <Box sx={{
          display: 'flex',
          flexGrow: 1,
          height: !isMobileOrTablet && "calc(100vh - 82px)",
          paddingTop: !isMobileOrTablet && "82px",
        }}>{renderMainContent()}</Box>
      </Container>
    </>
  );
};

export default Layout;