import React from 'react';
import Markdown from 'markdown-to-jsx';
import LinkComponent from "./LinkComponent";
import { renderTextWithTags } from "./Utils";

interface StaticMarkdownProps {
    text: string;
    sources?: any;
    plotData?: any;
    onViewOnPlotClicked: (payload: { citation_ids: string[]; messageId: string }) => void;
    onViewOnPlotHover: (payload: { citation_ids: string[]; messageId: string }) => void;
    onViewOnSourceHover: (payload: { paper_ids: string[]; messageId: string }) => void;
    onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
    messageId?: string;
}

const StaticMarkdown = ({
    text,
    sources,
    plotData,
    onViewOnPlotClicked,
    onViewOnPlotHover,
    onViewOnSourceHover,
    onViewOnSourceClicked,
    messageId
}: StaticMarkdownProps) => {
    const markdownOptions = {
        overrides: {
            ol: {
                component: (props: any) => (
                    <ol start={props.start}>
                        {React.Children.map(props.children, (child: any, index: number) => (
                            <li key={index}>
                                {React.Children.map(child.props.children, (grandChild: any) => {
                                    if (React.isValidElement(grandChild) && grandChild.type === 'a') {
                                        return (
                                            <LinkComponent
                                                {...grandChild.props}
                                                messageId={messageId || ''}
                                                onViewOnPlotClicked={onViewOnPlotClicked}
                                                onViewOnPlotHover={onViewOnPlotHover}
                                                onViewOnSourceHover={onViewOnSourceHover}
                                                onViewOnSourceClicked={onViewOnSourceClicked}
                                            />
                                        );
                                    }
                                    return grandChild;
                                })}
                            </li>
                        ))}
                    </ol>
                ),
            },
            li: {
                component: (props: any) => (
                    <li>
                        {React.Children.map(props.children, (child: any) => {
                            if (React.isValidElement(child) && child.type === 'a') {
                                return (
                                    <LinkComponent
                                        {...child.props}
                                        messageId={messageId || ''}
                                        onViewOnPlotClicked={onViewOnPlotClicked}
                                        onViewOnPlotHover={onViewOnPlotHover}
                                        onViewOnSourceHover={onViewOnSourceHover}
                                        onViewOnSourceClicked={onViewOnSourceClicked}
                                    />
                                );
                            }
                            return child;
                        })}
                    </li>
                ),
            },
            h3: {
                component: (props: any) => (<h3 {...props} />)
            },
            p: {
                component: (props: any) => (
                    <div>
                        {React.Children.map(props.children, (child: any) => {
                            if (React.isValidElement(child) && child.type === 'a') {
                                return (
                                    <LinkComponent
                                        {...child.props}
                                        messageId={messageId || ''}
                                        onViewOnPlotClicked={onViewOnPlotClicked}
                                        onViewOnPlotHover={onViewOnPlotHover}
                                        onViewOnSourceHover={onViewOnSourceHover}
                                        onViewOnSourceClicked={onViewOnSourceClicked}
                                    />
                                );
                            }
                            return child;
                        })}
                    </div>
                ),
            },
            em: {
                component: ({ children }: { children: React.ReactNode }) => <em>{children}</em>
            },
        },
    };

    return (
        <Markdown
            className="markdown-container"
            options={markdownOptions}
        >
            {renderTextWithTags(text, sources, plotData)}
        </Markdown>
    );
};

export default StaticMarkdown;