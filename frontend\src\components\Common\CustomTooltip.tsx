import React, { ReactNode } from 'react';
import Tooltip, { TooltipProps } from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { useTheme } from '@mui/material/styles';

interface CustomTooltipProps extends Omit<TooltipProps, 'title'> {
  children: ReactNode;
  content: ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'subtitle1' | 'subtitle2' | 'body1' | 'body2' | 'caption' | 'button' | 'overline' | 'inherit';
  fontSize?: string | number;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({
  children,
  content,
  variant = 'caption',
  fontSize = '12px',
  ...rest
}) => {
  const theme = useTheme();

  return (
    <Tooltip
      {...rest}
      slotProps={{
        tooltip: {
          sx: {
            borderRadius: '4px',
            backgroundColor: theme.components.tooltip.fill,
            border: 'none',
            maxWidth: 200,
          },
        },
      }}
      title={
        <Typography
          variant={variant}
          sx={{
            color: theme.common.white.main,
            border: 'none',
            whiteSpace: 'normal',
            fontSize: fontSize,
            fontFamily: "Roboto",
          }}
        >
          {content}
        </Typography>
      }
    >
      {children}
    </Tooltip>
  );
};

export default CustomTooltip;