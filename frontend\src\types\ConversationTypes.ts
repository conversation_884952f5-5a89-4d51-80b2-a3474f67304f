export interface ConversationResponse {
    success: boolean;
    data: {
        messages: Message[];
    };
}

export interface ConversationNewMessagedResponse {
    success: boolean;
    data: {
        message_id: string;
        information_message_id: string;
    };
}

export interface Source {
    id: string;
    paper_id: string;
    short_paper_id: string;
    title: string;
    doi_url: string | null;
    citation: string;
    position?: number;
    journal_name?: string;
}

export interface DownloadLinks {
    csv?: string;
    json?: string;
}

export interface PlotDataInfo {
    default_tab: string;
    effect_sizes: EffectSize[];
    studies: Study[];
}

export interface PlotData {
    type: string;
    data?: PlotDataInfo;
}

export interface Plot {
    title?: string;
    data?: PlotData;
    download_links?: DownloadLinks;
    activeMessageId?: string;
}

export interface Message {
    id: string;
    conversation_id: string | undefined;
    text?: string;
    type: 'information' | 'answer' | 'question';
    author: 'system' | 'user';
    created_at?: string;
    updated_at?: string;
    plot?: Plot;
    sources?: Source[];
    download_links?: DownloadLinks;
    choices?: string[];
}

export interface AggregateData {
    lower: number;
    upper: number;
    value: number;
}

export interface DataPoint {
    country: string;
    label: string;
    paper_citation: string;
    paper_id: number;
    paper_title: number;
    score: AggregateData;
}

export interface EffectSize {
    aggregate: AggregateData;
    data: DataPoint[];
    intervention: string;
    intervention_id: number;
    outcome: string;
    outcome_id: number;
}

export interface Study {
    country: string;
    id: number;
    label: string;
    population: string;
    pulication_year: number;
    quality_score: number;
    region: string;
}


export interface SuggestedTopicsChoices {
    type: 'suggested_topics';
    options: string[];
}

export interface RelatedTopicsChoices {
    type: 'related_topics';
    options: string[];
}

export interface Option {
    ref: string;
    label: string;
    value: string;
    description: string;
}

export interface SourcesResponse {
    success: boolean;
    data: {
        sources: Source[];
    };
}
export interface PlotScore {
    lower: number;
    upper: number;
    value: number;
}

export interface PlotDataPoint {
    label: string;
    score: PlotScore;
    title: string;
    paper_id: number;
}

export interface PlotOutcome {
    data: Record<string, PlotDataPoint>;
    outcome: string;
    aggregate: PlotScore;
    outcome_id: number;
    intervention: string;
    intervention_id: number;
}

export interface PlotDownloadLinks {
    csv: string;
    json: string;
}

export interface PlotResponse {
    success?: boolean;
    data: {
        id: string;
        title: string;
        data: PlotOutcome[];
        download_links: PlotDownloadLinks;
    };
}

export interface TopicDetails {
    id: number;
    type: string;
    count: number;
    label: string;
    level: string;
    request: string;
}

export interface ChoiceOption {
    ref: string;
    label: string;
    topic: TopicDetails;
    value: string;
    description: string;
}

export interface FollowUpResponse {
    success: boolean;
    data: {
        id: string;
        conversation_id: string;
        text: string;
        author: string;
        type: string;
        choices?: string[];
        created_at: string;
        updated_at: string;
    };
}

export interface DataItem {
    label?: string;
    score?: PlotScore;
    country?: string;
    paper_id?: number;
    paper_title?: string;
    paper_citation?: string;
}

export interface TagEntry {
    id: number;
    label: string;
    value: string;
}

export interface TagsApiResponse {
    success: boolean;
    data: {
        entries: TagEntry[];
    };
}